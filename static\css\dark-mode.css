/* Dark Mode CSS for ALEMIS */

[data-theme="dark"] {
    /* الألوان الأساسية */
    --primary-color: #3a67d1;
    --primary-dark: #2a57c1;
    --primary-light: #5a87f1;
    --secondary-color: #ff3b47;
    --secondary-dark: #e02030;
    --secondary-light: #ff5b67;
    --warning-color: #ffb74d;
    --danger-color: #ff5252;
    --info-color: #4fc3f7;
    
    /* ألوان الخلفية والنص */
    --dark-color: #e0e0e0;
    --light-color: #1e1e1e;
    --gray-color: #aaaaaa;
    --gray-light: #333333;
    --body-bg: #121212;
    --card-bg: #1e1e1e;
    
    /* ظلال العناصر */
    --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    --card-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
}

/* تطبيق الألوان على العناصر */
[data-theme="dark"] body {
    background-color: var(--body-bg);
    color: var(--dark-color);
}

/* تنسيق شريط التمرير */
[data-theme="dark"] ::-webkit-scrollbar-track {
    background: var(--gray-light);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
    background: var(--gray-color);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* تنسيق العناوين */
[data-theme="dark"] h1, 
[data-theme="dark"] h2, 
[data-theme="dark"] h3, 
[data-theme="dark"] h4, 
[data-theme="dark"] h5, 
[data-theme="dark"] h6 {
    color: var(--dark-color);
}

/* تنسيق القائمة العلوية */
[data-theme="dark"] .navbar-dark {
    background: linear-gradient(135deg, #1a237e, #283593) !important;
}

/* تنسيق البطاقات */
[data-theme="dark"] .card {
    background-color: var(--card-bg);
    box-shadow: var(--card-shadow);
}

[data-theme="dark"] .card:hover {
    box-shadow: var(--card-shadow-hover);
}

[data-theme="dark"] .card-header {
    background: linear-gradient(135deg, #1a237e, #283593);
}

[data-theme="dark"] .card-header.bg-secondary {
    background: linear-gradient(135deg, #b71c1c, #c62828) !important;
}

[data-theme="dark"] .card-body {
    color: var(--dark-color);
}

[data-theme="dark"] .card-footer {
    background-color: rgba(255, 255, 255, 0.05);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* تنسيق الأزرار */
[data-theme="dark"] .btn-primary {
    background-color: var(--primary-color);
}

[data-theme="dark"] .btn-primary:hover {
    background-color: var(--primary-dark);
}

[data-theme="dark"] .btn-success {
    background-color: var(--secondary-color);
}

[data-theme="dark"] .btn-success:hover {
    background-color: var(--secondary-dark);
}

/* تنسيق النماذج */
[data-theme="dark"] .form-control, 
[data-theme="dark"] .form-select {
    background-color: rgba(30, 30, 30, 0.8);
    border: 1px solid var(--gray-light);
    color: var(--dark-color);
}

[data-theme="dark"] .form-control:focus, 
[data-theme="dark"] .form-select:focus {
    background-color: rgba(40, 40, 40, 0.9);
    border-color: var(--primary-light);
    color: var(--dark-color);
}

[data-theme="dark"] .form-label {
    color: var(--dark-color);
}

/* تنسيق الجداول */
[data-theme="dark"] .table {
    color: var(--dark-color);
}

[data-theme="dark"] .table-striped > tbody > tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .table-hover > tbody > tr:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .table-bordered {
    border-color: var(--gray-light);
}

/* تنسيق القوائم المنسدلة */
[data-theme="dark"] .dropdown-menu {
    background-color: var(--card-bg);
    border: 1px solid var(--gray-light);
}

[data-theme="dark"] .dropdown-item {
    color: var(--dark-color);
}

[data-theme="dark"] .dropdown-item:hover {
    background-color: var(--primary-dark);
    color: white;
}

/* تنسيق التنبيهات */
[data-theme="dark"] .alert {
    background-color: var(--card-bg);
    border: 1px solid var(--gray-light);
    color: var(--dark-color);
}

[data-theme="dark"] .alert-success {
    border-right: 4px solid var(--secondary-color);
}

[data-theme="dark"] .alert-danger {
    border-right: 4px solid var(--danger-color);
}

[data-theme="dark"] .alert-warning {
    border-right: 4px solid var(--warning-color);
}

[data-theme="dark"] .alert-info {
    border-right: 4px solid var(--info-color);
}

/* تنسيق تذييل الصفحة */
[data-theme="dark"] footer {
    background-color: var(--card-bg) !important;
    color: var(--dark-color);
}

[data-theme="dark"] footer a.text-dark {
    color: var(--dark-color) !important;
}

[data-theme="dark"] footer .text-center {
    background-color: rgba(0, 0, 0, 0.2) !important;
}

/* تنسيق زر تبديل الوضع */
.theme-switch-wrapper {
    display: flex;
    align-items: center;
    margin-left: 15px;
}

.theme-switch {
    display: inline-block;
    position: relative;
    width: 50px;
    height: 24px;
}

.theme-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider .sun-icon,
.slider .moon-icon {
    position: absolute;
    top: 4px;
    font-size: 14px;
    transition: .4s;
}

.slider .sun-icon {
    left: 6px;
    color: #f39c12;
    opacity: 1;
}

.slider .moon-icon {
    right: 6px;
    color: #f1c40f;
    opacity: 0;
}

input:checked + .slider .sun-icon {
    opacity: 0;
}

input:checked + .slider .moon-icon {
    opacity: 1;
}
