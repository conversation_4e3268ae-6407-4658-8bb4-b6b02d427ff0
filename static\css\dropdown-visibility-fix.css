/* إصلاح شامل لمشكلة عدم ظهور القوائم المنسدلة بشكل كامل */

/* إصلاح جذري للحاويات */
html {
    overflow-x: visible !important;
    overflow-y: auto !important;
}

body {
    overflow-x: visible !important;
    overflow-y: auto !important;
    position: relative !important;
}

/* إصلاح الحاويات الأساسية */
.container,
.container-fluid,
.container-sm,
.container-md,
.container-lg,
.container-xl,
.container-xxl {
    overflow: visible !important;
    position: relative !important;
    z-index: auto !important;
}

/* إصلاح الشريط العلوي */
.navbar {
    overflow: visible !important;
    position: relative !important;
    z-index: 1030 !important;
}

.navbar .container {
    overflow: visible !important;
    position: relative !important;
}

.navbar-collapse {
    overflow: visible !important;
    position: relative !important;
}

.navbar-nav {
    overflow: visible !important;
    position: relative !important;
}

.nav-item {
    overflow: visible !important;
    position: relative !important;
}

/* إصلاح القوائم المنسدلة */
.dropdown {
    position: relative !important;
    overflow: visible !important;
}

.dropdown-toggle {
    position: relative !important;
    z-index: 1060 !important;
}

.dropdown-menu {
    position: absolute !important;
    z-index: 1055 !important;
    overflow: visible !important;
    max-height: none !important;
    height: auto !important;
    width: auto !important;
    min-width: 220px !important;
    max-width: 350px !important;
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
    transform: translateY(-5px) scale(0.98) !important;
    transition: all 0.15s ease !important;
    background: white !important;
    border: 1px solid rgba(0,0,0,0.12) !important;
    border-radius: 8px !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    padding: 8px 0 !important;
    margin: 2px 0 0 0 !important;
    white-space: nowrap !important;
    text-align: right !important;
    direction: rtl !important;
}

.dropdown-menu.show {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    pointer-events: auto !important;
    transform: translateY(0) scale(1) !important;
}

/* إصلاح عناصر القائمة */
.dropdown-item {
    display: flex !important;
    align-items: center !important;
    padding: 10px 16px !important;
    color: #374151 !important;
    text-decoration: none !important;
    background: transparent !important;
    border: none !important;
    width: 100% !important;
    text-align: right !important;
    white-space: nowrap !important;
    transition: all 0.15s ease !important;
    cursor: pointer !important;
}

.dropdown-item:hover,
.dropdown-item:focus {
    background: rgba(14, 165, 233, 0.08) !important;
    color: #0ea5e9 !important;
    text-decoration: none !important;
}

.dropdown-item i {
    width: 18px !important;
    margin-left: 10px !important;
    text-align: center !important;
    font-size: 0.9rem !important;
    color: #6b7280 !important;
    transition: color 0.15s ease !important;
}

.dropdown-item:hover i,
.dropdown-item:focus i {
    color: #0ea5e9 !important;
}

/* إصلاح خاص للشريط العلوي */
.navbar .dropdown-menu {
    top: calc(100% + 2px) !important;
    bottom: auto !important;
    left: 0 !important;
    right: auto !important;
}

.navbar .dropdown-menu-end {
    left: auto !important;
    right: 0 !important;
}

/* إصلاح للقوائم في الجانب الأيمن */
.navbar-nav:last-child .dropdown-menu,
.navbar .dropdown:last-child .dropdown-menu {
    left: auto !important;
    right: 0 !important;
}

/* إصلاح للقوائم في الجانب الأيسر */
.navbar-nav:first-child .dropdown-menu,
.navbar .dropdown:first-child .dropdown-menu {
    left: 0 !important;
    right: auto !important;
}

/* إصلاح مشاكل التداخل */
.dropdown-menu * {
    box-sizing: border-box !important;
}

/* إصلاح للشاشات الصغيرة */
@media (max-width: 768px) {
    .dropdown-menu {
        min-width: 200px !important;
        max-width: 300px !important;
        font-size: 0.85rem !important;
    }
    
    .dropdown-item {
        padding: 8px 12px !important;
    }
}

/* إصلاح خاص للقوائم الطويلة */
.dropdown-menu {
    max-height: 400px !important;
    overflow-y: auto !important;
    overflow-x: visible !important;
}

/* إصلاح للقوائم التي تحتوي على عناصر كثيرة */
.dropdown-menu::-webkit-scrollbar {
    width: 6px !important;
}

.dropdown-menu::-webkit-scrollbar-track {
    background: #f1f1f1 !important;
    border-radius: 3px !important;
}

.dropdown-menu::-webkit-scrollbar-thumb {
    background: #c1c1c1 !important;
    border-radius: 3px !important;
}

.dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8 !important;
}

/* إصلاح للفواصل في القوائم */
.dropdown-divider {
    height: 0 !important;
    margin: 8px 0 !important;
    overflow: hidden !important;
    border-top: 1px solid #e9ecef !important;
}

.dropdown-header {
    display: block !important;
    padding: 8px 16px !important;
    margin-bottom: 0 !important;
    font-size: 0.875rem !important;
    color: #6c757d !important;
    white-space: nowrap !important;
}

/* إصلاح للنصوص في القوائم */
.dropdown-item-text {
    display: block !important;
    padding: 8px 16px !important;
    color: #212529 !important;
}

/* إصلاح خاص للإشعارات */
.notification-dropdown {
    min-width: 300px !important;
    max-width: 400px !important;
}

.notification-item {
    border-bottom: 1px solid #eee !important;
}

.notification-item:last-child {
    border-bottom: none !important;
}

.notification-text {
    font-size: 0.9rem !important;
    margin-right: 8px !important;
}

/* إصلاح للأزرار في القوائم */
.dropdown-menu .btn {
    margin: 4px 8px !important;
    font-size: 0.875rem !important;
}

/* إصلاح للشارات في القوائم */
.dropdown-menu .badge {
    margin-left: 8px !important;
}

/* إصلاح نهائي للظهور */
.dropdown.show .dropdown-menu {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    pointer-events: auto !important;
    transform: translateY(0) scale(1) !important;
}

/* إصلاح للتأثيرات الحركية */
@keyframes dropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(-5px) scale(0.98);
        visibility: visible;
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
        visibility: visible;
    }
}

.dropdown-menu.show {
    animation: dropdownFadeIn 0.15s ease-out !important;
}

/* إصلاح للوضع المظلم */
[data-theme="dark"] .dropdown-menu {
    background: #374151 !important;
    border-color: #4b5563 !important;
    color: #f3f4f6 !important;
}

[data-theme="dark"] .dropdown-item {
    color: #f3f4f6 !important;
}

[data-theme="dark"] .dropdown-item:hover,
[data-theme="dark"] .dropdown-item:focus {
    background: rgba(96, 165, 250, 0.1) !important;
    color: #60a5fa !important;
}

[data-theme="dark"] .dropdown-divider {
    border-top-color: #4b5563 !important;
}

[data-theme="dark"] .dropdown-header {
    color: #9ca3af !important;
}

/* إصلاح أخير للتأكد من الظهور */
.navbar .dropdown-menu,
.dropdown .dropdown-menu {
    will-change: transform, opacity !important;
    backface-visibility: hidden !important;
}
