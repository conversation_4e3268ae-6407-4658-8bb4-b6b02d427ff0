#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from datetime import datetime

def add_document_column():
    """إضافة عمود document_path إلى جدول leave_requests"""

    conn = sqlite3.connect('alemis.db')
    cursor = conn.cursor()

    try:
        print("📄 إضافة عمود المستندات إلى جدول طلبات الإجازة...")

        # Check if column already exists
        cursor.execute("PRAGMA table_info(leave_requests)")
        columns = [column[1] for column in cursor.fetchall()]

        if 'document_path' not in columns:
            # Add document_path column
            cursor.execute('ALTER TABLE leave_requests ADD COLUMN document_path TEXT')
            print("✅ تم إضافة عمود document_path بنجاح")
        else:
            print("ℹ️ عمود document_path موجود بالفعل")

        # Add comments column for manager/hr comments
        if 'manager_comment' not in columns:
            cursor.execute('ALTER TABLE leave_requests ADD COLUMN manager_comment TEXT')
            print("✅ تم إضافة عمود manager_comment بنجاح")
        else:
            print("ℹ️ عمود manager_comment موجود بالفعل")

        if 'hr_comment' not in columns:
            cursor.execute('ALTER TABLE leave_requests ADD COLUMN hr_comment TEXT')
            print("✅ تم إضافة عمود hr_comment بنجاح")
        else:
            print("ℹ️ عمود hr_comment موجود بالفعل")

        if 'hospital_manager_approval' not in columns:
            cursor.execute('ALTER TABLE leave_requests ADD COLUMN hospital_manager_approval INTEGER DEFAULT 0')
            print("✅ تم إضافة عمود hospital_manager_approval بنجاح")
        else:
            print("ℹ️ عمود hospital_manager_approval موجود بالفعل")

        if 'hospital_manager_comment' not in columns:
            cursor.execute('ALTER TABLE leave_requests ADD COLUMN hospital_manager_comment TEXT')
            print("✅ تم إضافة عمود hospital_manager_comment بنجاح")
        else:
            print("ℹ️ عمود hospital_manager_comment موجود بالفعل")

        if 'coverage_day_ids' not in columns:
            cursor.execute('ALTER TABLE leave_requests ADD COLUMN coverage_day_ids TEXT')
            print("✅ تم إضافة عمود coverage_day_ids بنجاح")
        else:
            print("ℹ️ عمود coverage_day_ids موجود بالفعل")

        conn.commit()
        print("✅ تم تحديث قاعدة البيانات بنجاح!")

    except Exception as e:
        print(f'❌ خطأ: {e}')
        conn.rollback()
    finally:
        conn.close()

if __name__ == '__main__':
    add_document_column()
