<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الشريط العلوي</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Dropdown Fix CSS -->
    <link rel="stylesheet" href="static/css/dropdown-fix.css">
    <style>
        /* Medical navbar */
        .medical-navbar {
            background: linear-gradient(135deg, #0ea5e9 0%, #0369a1 100%) !important;
            box-shadow: 0 2px 10px rgba(14, 165, 233, 0.3);
            min-height: 70px;
            padding: 0.5rem 0;
        }

        /* Navbar improvements */
        .navbar-nav .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500;
            padding: 0.75rem 1rem !important;
            border-radius: 8px;
            margin: 0 0.25rem;
            transition: all 0.3s ease;
            display: flex !important;
            align-items: center !important;
        }

        .navbar-nav .nav-link:hover {
            color: #ffffff !important;
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-1px);
        }

        /* Dropdown improvements */
        .dropdown-menu {
            border: none;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-radius: 12px;
            padding: 0.5rem 0;
            margin-top: 0.5rem;
        }

        .dropdown-item {
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex !important;
            align-items: center !important;
        }

        .dropdown-item:hover {
            background: rgba(14, 165, 233, 0.1);
            color: #0ea5e9;
        }

        .dropdown-item i {
            width: 20px;
            margin-left: 0.75rem;
        }

        .medical-brand {
            font-weight: 700;
            color: white !important;
        }

        .brand-container {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .medical-logo {
            width: 45px;
            height: 45px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .brand-text {
            display: flex;
            flex-direction: column;
            line-height: 1.2;
        }

        .brand-name {
            font-size: 1.4rem;
            font-weight: 700;
            color: white;
        }

        .brand-subtitle {
            font-size: 0.85rem;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
        }

        /* Theme switch */
        .theme-switch-wrapper {
            display: flex;
            align-items: center;
        }

        .theme-switch {
            display: inline-block;
            height: 34px;
            position: relative;
            width: 60px;
        }

        .theme-switch input {
            display: none;
        }

        .slider {
            background-color: #ccc;
            bottom: 0;
            cursor: pointer;
            left: 0;
            position: absolute;
            right: 0;
            top: 0;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            background-color: #fff;
            bottom: 4px;
            content: "";
            height: 26px;
            left: 4px;
            position: absolute;
            transition: .4s;
            width: 26px;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #2196F3;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .sun-icon, .moon-icon {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            font-size: 14px;
            z-index: 1;
        }

        .sun-icon {
            left: 8px;
            color: #f39c12;
        }

        .moon-icon {
            right: 8px;
            color: #f1c40f;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark medical-navbar">
        <div class="container">
            <a class="navbar-brand medical-brand" href="#">
                <div class="brand-container">
                    <div class="medical-logo">
                        <svg width="30" height="30" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="50" cy="50" r="45" fill="url(#logoGradient)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
                            <rect x="42" y="25" width="16" height="50" fill="white" rx="2"/>
                            <rect x="25" y="42" width="50" height="16" fill="white" rx="2"/>
                            <circle cx="50" cy="50" r="8" fill="rgba(14, 165, 233, 0.8)"/>
                            <defs>
                                <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#0ea5e9;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#0369a1;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                        </svg>
                    </div>
                    <div class="brand-text">
                        <span class="brand-name">العميس الطبية</span>
                        <small class="brand-subtitle">نظام إدارة موظفين المختبر</small>
                    </div>
                </div>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                        </a>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="usersDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-users me-1"></i>المستخدمين
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user-friends"></i> قائمة المستخدمين</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user-plus"></i> إضافة مستخدم جديد</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-building"></i> إدارة الأقسام</a></li>
                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="requestsDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-clipboard-check me-1"></i>طلبات الموظفين
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-calendar-alt"></i> طلبات الإجازات</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-exchange-alt"></i> طلبات التغطية</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-sync-alt"></i> طلبات تبديل الدوام</a></li>
                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="reportsDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-chart-bar me-1"></i>التقارير والجداول
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-chart-pie"></i> التقارير</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-calendar"></i> جدول الإجازات</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-calendar-week"></i> جدول الشفتات</a></li>
                        </ul>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <!-- زر تبديل الوضع الداكن -->
                    <li class="nav-item">
                        <div class="theme-switch-wrapper">
                            <label class="theme-switch" for="checkbox">
                                <input type="checkbox" id="checkbox" />
                                <div class="slider">
                                    <i class="fas fa-sun sun-icon"></i>
                                    <i class="fas fa-moon moon-icon"></i>
                                </div>
                            </label>
                        </div>
                    </li>

                    <!-- زر الإشعارات -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-bell me-1"></i>
                            <span class="badge rounded-pill bg-danger">3</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li class="dropdown-header">الإشعارات</li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-calendar-alt text-primary"></i> طلبات إجازات جديدة</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-exchange-alt text-success"></i> طلبات تغطية جديدة</a></li>
                        </ul>
                    </li>

                    <!-- المساعدة -->
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-question-circle me-1"></i>المساعدة
                        </a>
                    </li>

                    <!-- ملف المستخدم -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>المستخدم
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-id-card"></i> الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog"></i> الإعدادات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="alert alert-success">
                    <h4><i class="fas fa-check-circle me-2"></i>اختبار الشريط العلوي</h4>
                    <p>هذه صفحة اختبار للتأكد من عمل الشريط العلوي والأيقونات بشكل صحيح.</p>
                    <hr>
                    <p class="mb-0">
                        <strong>التحقق من:</strong>
                        <br>✅ ظهور جميع الأيقونات
                        <br>✅ عمل القوائم المنسدلة
                        <br>✅ تبديل الوضع المظلم
                        <br>✅ الاستجابة للشاشات المختلفة
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Dropdown Fix JS -->
    <script src="static/js/dropdown-fix.js"></script>
    <!-- Dropdown Position Fix JS -->
    <script src="static/js/dropdown-position-fix.js"></script>

    <script>
        // تفعيل القوائم المنسدلة بطريقة محسنة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Starting enhanced dropdown test...');

            // طريقة 1: Bootstrap الافتراضي
            try {
                var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
                var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
                    return new bootstrap.Dropdown(dropdownToggleEl);
                });
                console.log('✅ Bootstrap dropdowns initialized:', dropdownList.length);
            } catch (error) {
                console.error('❌ Bootstrap dropdown error:', error);
            }

            // طريقة 2: معالج يدوي احتياطي
            setTimeout(() => {
                const dropdowns = document.querySelectorAll('.dropdown-toggle');
                console.log(`🔧 Manual dropdown setup for ${dropdowns.length} elements`);

                dropdowns.forEach((dropdown, index) => {
                    dropdown.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        const menu = this.nextElementSibling;
                        if (menu && menu.classList.contains('dropdown-menu')) {
                            // إغلاق جميع القوائم الأخرى
                            document.querySelectorAll('.dropdown-menu.show').forEach(otherMenu => {
                                if (otherMenu !== menu) {
                                    otherMenu.classList.remove('show');
                                }
                            });

                            // تبديل القائمة الحالية
                            menu.classList.toggle('show');
                            console.log(`📋 Dropdown ${index + 1} toggled`);
                        }
                    });
                });

                // إغلاق القوائم عند النقر خارجها
                document.addEventListener('click', function(e) {
                    if (!e.target.closest('.dropdown')) {
                        document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                            menu.classList.remove('show');
                        });
                    }
                });

                console.log('✅ Manual dropdown handlers added');
            }, 500);

            // تفعيل تبديل الوضع المظلم
            const toggleSwitch = document.querySelector('#checkbox');
            if (toggleSwitch) {
                toggleSwitch.addEventListener('change', function(e) {
                    if (e.target.checked) {
                        document.body.style.background = '#1a1a1a';
                        document.body.style.color = '#ffffff';
                    } else {
                        document.body.style.background = '#ffffff';
                        document.body.style.color = '#000000';
                    }
                });
            }

            console.log('✅ Enhanced navbar test page loaded successfully');
        });
    </script>
</body>
</html>
