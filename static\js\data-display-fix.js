// إصلاح عرض البيانات - ALEMEIS System
(function() {
    'use strict';
    
    // تشغيل الإصلاح عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🔧 Starting data display fix...');
        
        // تشغيل الإصلاح مع تأخير للتأكد من تحميل جميع العناصر
        setTimeout(fixAllDataDisplay, 500);
        setTimeout(fixAllDataDisplay, 1500); // إصلاح إضافي
        
        // مراقبة التغييرات في DOM
        observeChanges();
    });
    
    function fixAllDataDisplay() {
        try {
            console.log('🔧 Fixing all data display...');
            
            // إصلاح الشريط العلوي
            fixNavbar();
            
            // إصلاح القوائم المنسدلة
            fixDropdowns();
            
            // إصلاح الأيقونات
            fixIcons();
            
            // إصلاح النصوص
            fixTexts();
            
            // إصلاح الأزرار
            fixButtons();
            
            console.log('✅ All data display fixed successfully');
            
        } catch (error) {
            console.error('❌ Error fixing data display:', error);
        }
    }
    
    function fixNavbar() {
        try {
            console.log('🔧 Fixing navbar...');
            
            // إصلاح الشريط العلوي نفسه
            const navbar = document.querySelector('.navbar');
            if (navbar) {
                navbar.style.display = 'block';
                navbar.style.visibility = 'visible';
                navbar.style.opacity = '1';
            }
            
            // إصلاح حاوي الشريط العلوي
            const navbarCollapse = document.querySelector('.navbar-collapse');
            if (navbarCollapse) {
                navbarCollapse.style.display = 'flex';
                navbarCollapse.style.visibility = 'visible';
                navbarCollapse.style.opacity = '1';
            }
            
            // إصلاح قوائم التنقل
            const navbarNavs = document.querySelectorAll('.navbar-nav');
            navbarNavs.forEach(nav => {
                nav.style.display = 'flex';
                nav.style.visibility = 'visible';
                nav.style.opacity = '1';
            });
            
            // إصلاح عناصر التنقل
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.style.display = 'block';
                item.style.visibility = 'visible';
                item.style.opacity = '1';
            });
            
            // إصلاح روابط التنقل
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.style.display = 'flex';
                link.style.alignItems = 'center';
                link.style.visibility = 'visible';
                link.style.opacity = '1';
                link.style.color = 'rgba(255, 255, 255, 0.9)';
                link.style.textDecoration = 'none';
            });
            
            console.log('✅ Navbar fixed');
            
        } catch (error) {
            console.error('❌ Error fixing navbar:', error);
        }
    }
    
    function fixDropdowns() {
        try {
            console.log('🔧 Fixing dropdowns...');
            
            // إصلاح القوائم المنسدلة
            const dropdownMenus = document.querySelectorAll('.dropdown-menu');
            dropdownMenus.forEach(menu => {
                menu.style.visibility = 'visible';
                menu.style.opacity = '1';
                menu.style.backgroundColor = '#ffffff';
                menu.style.border = '1px solid rgba(0,0,0,.15)';
                menu.style.borderRadius = '0.5rem';
                menu.style.boxShadow = '0 0.5rem 1rem rgba(0, 0, 0, 0.175)';
            });
            
            // إصلاح عناصر القوائم المنسدلة
            const dropdownItems = document.querySelectorAll('.dropdown-item');
            dropdownItems.forEach(item => {
                item.style.display = 'flex';
                item.style.alignItems = 'center';
                item.style.visibility = 'visible';
                item.style.opacity = '1';
                item.style.color = '#374151';
                item.style.textDecoration = 'none';
                item.style.padding = '0.75rem 1.5rem';
                item.style.fontSize = '0.95rem';
                item.style.fontWeight = '500';
            });
            
            console.log('✅ Dropdowns fixed');
            
        } catch (error) {
            console.error('❌ Error fixing dropdowns:', error);
        }
    }
    
    function fixIcons() {
        try {
            console.log('🔧 Fixing icons...');
            
            // إصلاح جميع الأيقونات
            const icons = document.querySelectorAll('.fas, .far, .fab, .fal');
            icons.forEach(icon => {
                icon.style.display = 'inline-block';
                icon.style.visibility = 'visible';
                icon.style.opacity = '1';
                icon.style.fontFamily = '"Font Awesome 6 Free", "Font Awesome 6 Pro"';
                icon.style.fontWeight = '900';
                icon.style.fontSize = 'inherit';
                icon.style.lineHeight = 'inherit';
            });
            
            // إصلاح أيقونات الشريط العلوي
            const navIcons = document.querySelectorAll('.nav-link i, .dropdown-item i');
            navIcons.forEach(icon => {
                icon.style.marginLeft = '0.5rem';
                icon.style.width = 'auto';
                icon.style.textAlign = 'center';
            });
            
            console.log('✅ Icons fixed');
            
        } catch (error) {
            console.error('❌ Error fixing icons:', error);
        }
    }
    
    function fixTexts() {
        try {
            console.log('🔧 Fixing texts...');
            
            // إصلاح النصوص في الشريط العلوي
            const navTexts = document.querySelectorAll('.nav-link, .dropdown-item');
            navTexts.forEach(element => {
                // إصلاح النصوص المباشرة
                const textNodes = Array.from(element.childNodes).filter(
                    node => node.nodeType === Node.TEXT_NODE && node.textContent.trim()
                );
                
                textNodes.forEach(textNode => {
                    const span = document.createElement('span');
                    span.textContent = textNode.textContent;
                    span.style.visibility = 'visible';
                    span.style.opacity = '1';
                    span.style.display = 'inline-block';
                    span.style.color = 'inherit';
                    textNode.parentNode.replaceChild(span, textNode);
                });
                
                // إصلاح النصوص داخل span
                const spans = element.querySelectorAll('span');
                spans.forEach(span => {
                    span.style.visibility = 'visible';
                    span.style.opacity = '1';
                    span.style.display = 'inline-block';
                    span.style.color = 'inherit';
                });
            });
            
            console.log('✅ Texts fixed');
            
        } catch (error) {
            console.error('❌ Error fixing texts:', error);
        }
    }
    
    function fixButtons() {
        try {
            console.log('🔧 Fixing buttons...');
            
            // إصلاح زر تبديل الوضع المظلم
            const themeSwitch = document.querySelector('.theme-switch-wrapper');
            if (themeSwitch) {
                themeSwitch.style.display = 'flex';
                themeSwitch.style.alignItems = 'center';
                themeSwitch.style.visibility = 'visible';
                themeSwitch.style.opacity = '1';
                
                const switchElement = themeSwitch.querySelector('.theme-switch');
                if (switchElement) {
                    switchElement.style.display = 'inline-block';
                    switchElement.style.visibility = 'visible';
                    switchElement.style.opacity = '1';
                }
            }
            
            // إصلاح زر القائمة المنسدلة للشاشات الصغيرة
            const navbarToggler = document.querySelector('.navbar-toggler');
            if (navbarToggler) {
                navbarToggler.style.display = 'block';
                navbarToggler.style.visibility = 'visible';
                navbarToggler.style.opacity = '1';
            }
            
            console.log('✅ Buttons fixed');
            
        } catch (error) {
            console.error('❌ Error fixing buttons:', error);
        }
    }
    
    function observeChanges() {
        try {
            // مراقبة التغييرات في DOM لإصلاح العناصر الجديدة
            const observer = new MutationObserver(function(mutations) {
                let shouldFix = false;
                
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        shouldFix = true;
                    }
                });
                
                if (shouldFix) {
                    setTimeout(fixAllDataDisplay, 100);
                }
            });
            
            // بدء المراقبة
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            
            console.log('✅ DOM observer started');
            
        } catch (error) {
            console.error('❌ Error setting up observer:', error);
        }
    }
    
    // تصدير الدوال للاستخدام الخارجي
    window.DataDisplayFix = {
        fixAll: fixAllDataDisplay,
        fixNavbar: fixNavbar,
        fixDropdowns: fixDropdowns,
        fixIcons: fixIcons,
        fixTexts: fixTexts,
        fixButtons: fixButtons
    };
    
    console.log('📦 Data display fix module loaded');
})();
