/* ملف الأنماط الطبية المتخصصة */

/* متغيرات الألوان الطبية */
:root {
    --medical-primary: #0ea5e9;
    --medical-primary-dark: #0284c7;
    --medical-primary-light: #7dd3fc;
    --medical-secondary: #64748b;
    --medical-success: #059669;
    --medical-warning: #d97706;
    --medical-danger: #dc2626;
    --medical-info: #0891b2;
    --medical-light: #f8fafc;
    --medical-dark: #1e293b;
    --medical-border: #e2e8f0;
    --medical-shadow: 0 4px 20px rgba(14, 165, 233, 0.15);
    --medical-shadow-hover: 0 8px 30px rgba(14, 165, 233, 0.25);
    --medical-gradient: linear-gradient(135deg, #0ea5e9 0%, #0284c7 50%, #0369a1 100%);
    --medical-gradient-light: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    --medical-red: #dc2626;
    --medical-red-light: #ef4444;
}

/* الشريط العلوي بالأزرق والخطوط الحمراء */
.medical-navbar {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 50%, #0369a1 100%);
    box-shadow: 0 4px 20px rgba(14, 165, 233, 0.3);
    padding: 0.75rem 0;
    position: relative;
    overflow: hidden;
    border-bottom: 4px solid var(--medical-red);
}

.medical-navbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        90deg,
        transparent,
        transparent 20px,
        rgba(220, 38, 38, 0.1) 20px,
        rgba(220, 38, 38, 0.1) 22px
    );
    z-index: 1;
}

.medical-navbar .container {
    position: relative;
    z-index: 2;
}

.medical-navbar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--medical-red), var(--medical-red-light), var(--medical-red));
    z-index: 3;
}

/* البطاقات الطبية */
.medical-card {
    border: none;
    border-radius: 16px;
    box-shadow: var(--medical-shadow);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    background: white;
    position: relative;
}

.medical-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--medical-gradient);
    z-index: 1;
}

.medical-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--medical-shadow-hover);
}

.medical-card-header {
    background: var(--medical-gradient);
    color: white;
    border: none;
    padding: 1.5rem 2rem;
    position: relative;
    overflow: hidden;
}

.medical-card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 100px;
    height: 100px;
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='rgba(255,255,255,0.1)'%3E%3Cpath d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'/%3E%3C/svg%3E") no-repeat center;
    background-size: contain;
    animation: rotate 20s linear infinite;
}

.medical-card-header-secondary {
    background: linear-gradient(135deg, #64748b 0%, #475569 50%, #334155 100%);
    color: white;
    border: none;
    padding: 1.5rem 2rem;
    position: relative;
    overflow: hidden;
}

.header-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
    font-size: 1.5rem;
    backdrop-filter: blur(10px);
}

.header-content h5 {
    font-weight: 600;
    font-size: 1.25rem;
}

.medical-card-body {
    padding: 2rem;
    background: var(--medical-gradient-light);
    color: var(--medical-dark) !important;
}

/* إصلاح مشكلة النصوص البيضاء */
.medical-card-body * {
    color: var(--medical-dark) !important;
}

.medical-card-body .text-white {
    color: white !important;
}

.medical-card-body .text-muted {
    color: var(--medical-secondary) !important;
}

.medical-card-body .text-primary {
    color: var(--medical-primary) !important;
}

.medical-card-body .text-success {
    color: var(--medical-success) !important;
}

.medical-card-body .text-danger {
    color: var(--medical-danger) !important;
}

.medical-card-body .text-warning {
    color: var(--medical-warning) !important;
}

.medical-card-body .text-info {
    color: var(--medical-info) !important;
}

/* الجداول الطبية */
.medical-table-container {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    background: white;
}

.medical-table {
    margin: 0;
    border: none;
}

.medical-table-header {
    background: var(--medical-gradient);
    color: white;
}

.medical-table-header th {
    border: none;
    padding: 1.25rem 1rem;
    font-weight: 600;
    font-size: 0.95rem;
    text-align: center;
    position: relative;
}

.medical-table-header th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: rgba(255, 255, 255, 0.3);
}

.medical-table-row {
    transition: all 0.3s ease;
    border-bottom: 1px solid var(--medical-border);
}

.medical-table-row:hover {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    transform: scale(1.01);
    box-shadow: 0 2px 10px rgba(14, 165, 233, 0.1);
}

.medical-table-row td {
    padding: 1.25rem 1rem;
    vertical-align: middle;
    border: none;
}

/* معلومات الموظف */
.employee-info {
    display: flex;
    align-items: center;
    font-size: 0.95rem;
}

.employee-info i {
    font-size: 1.2rem;
}

/* الشارات الطبية */
.medical-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 500;
    font-size: 0.85rem;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.badge-username {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border-color: #3b82f6;
}

.badge-password {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    border-color: #f59e0b;
}

.badge-email {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
    color: white;
    border-color: #06b6d4;
}

.badge-personal {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border-color: #10b981;
}

/* الشارات المعالجة */
.badge-username-processed {
    background: rgba(59, 130, 246, 0.1);
    color: #1d4ed8;
    border-color: #3b82f6;
}

.badge-password-processed {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border-color: #f59e0b;
}

.badge-email-processed {
    background: rgba(6, 182, 212, 0.1);
    color: #0891b2;
    border-color: #06b6d4;
}

.badge-personal-processed {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
    border-color: #10b981;
}

/* شارات الحالة */
.medical-status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 500;
    font-size: 0.85rem;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.status-approved {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.status-rejected {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

/* قيم البيانات */
.data-value {
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
}

.current-value {
    background: rgba(100, 116, 139, 0.1);
    color: var(--medical-secondary);
}

.new-value {
    background: rgba(14, 165, 233, 0.1);
    color: var(--medical-primary-dark);
}

/* نص السبب */
.reason-text {
    font-size: 0.9rem;
    color: var(--medical-dark);
    font-style: italic;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* معلومات التاريخ */
.date-info {
    font-size: 0.85rem;
    color: var(--medical-secondary);
    font-weight: 500;
}

/* أزرار الإجراءات */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.medical-btn {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 500;
    font-size: 0.85rem;
    border: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    position: relative;
    overflow: hidden;
}

.medical-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.medical-btn:hover::before {
    left: 100%;
}

.medical-btn-approve {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.medical-btn-approve:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.medical-btn-reject {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.medical-btn-reject:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
}

/* التنبيهات الطبية */
.medical-alert {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    border-radius: 12px;
    border: none;
    margin: 1rem 0;
}

.medical-alert-info {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    color: var(--medical-info);
    border-left: 4px solid var(--medical-info);
}

.alert-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
    font-size: 1.5rem;
}

.alert-content h6 {
    font-weight: 600;
    color: var(--medical-dark);
}

.alert-content p {
    color: var(--medical-secondary);
    font-size: 0.9rem;
}

/* الحركات والتأثيرات */
@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.8s ease-out forwards;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .medical-card-body {
        padding: 1rem;
    }

    .medical-table-header th,
    .medical-table-row td {
        padding: 0.75rem 0.5rem;
        font-size: 0.85rem;
    }

    .action-buttons {
        flex-direction: column;
    }

    .medical-btn {
        width: 100%;
        justify-content: center;
    }

    .reason-text {
        max-width: 150px;
    }

    .page-title {
        font-size: 2rem;
    }

    .medical-icon {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .medical-header {
        padding: 1rem 0;
    }

    .header-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .medical-table-container {
        font-size: 0.8rem;
    }
}

/* تصميم Footer الطبي */
.medical-footer {
    background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.medical-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cpath d='M50 10 L60 40 L90 40 L68 58 L78 88 L50 70 L22 88 L32 58 L10 40 L40 40 Z' fill='rgba(14,165,233,0.05)'/%3E%3C/svg%3E") repeat;
    opacity: 0.3;
    animation: footerPattern 40s linear infinite;
}

@keyframes footerPattern {
    0% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(50px) translateY(-25px); }
    50% { transform: translateX(0) translateY(-50px); }
    75% { transform: translateX(-50px) translateY(-25px); }
    100% { transform: translateX(0) translateY(0); }
}

.footer-content {
    position: relative;
    z-index: 2;
}

.footer-brand {
    position: relative;
}

.footer-logo {
    transition: all 0.3s ease;
    filter: drop-shadow(0 2px 8px rgba(0,0,0,0.3));
}

.footer-logo:hover {
    transform: rotate(10deg) scale(1.1);
}

.footer-title {
    font-weight: 700;
    font-size: 1.5rem;
    color: white;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin: 0;
}

.footer-description {
    color: rgba(255,255,255,0.8);
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.medical-features {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    color: rgba(255,255,255,0.9);
    transition: all 0.3s ease;
}

.feature-item:hover {
    color: white;
    transform: translateX(5px);
}

.footer-section-title {
    font-weight: 600;
    font-size: 1.1rem;
    color: white;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid rgba(14, 165, 233, 0.3);
    position: relative;
}

.footer-section-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--medical-primary);
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.75rem;
}

.footer-links a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.footer-links a:hover {
    color: var(--medical-primary);
    transform: translateX(5px);
}

.footer-contact {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-contact li {
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    color: rgba(255,255,255,0.8);
    transition: all 0.3s ease;
}

.footer-contact li:hover {
    color: white;
    transform: translateX(3px);
}

.footer-contact i {
    width: 20px;
    text-align: center;
}

.footer-bottom {
    background: rgba(0,0,0,0.2);
    border-top: 1px solid rgba(255,255,255,0.1);
    padding: 1rem 0;
    position: relative;
    z-index: 2;
}

.footer-bottom p {
    color: rgba(255,255,255,0.8);
    font-size: 0.9rem;
}

.footer-badges {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
    flex-wrap: wrap;
}

.footer-badges .badge {
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.footer-badges .badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
}

/* تصميم متجاوب للـ Footer */
@media (max-width: 768px) {
    .footer-badges {
        justify-content: center;
        margin-top: 1rem;
    }

    .footer-bottom .col-md-6:last-child {
        text-align: center !important;
    }

    .medical-features {
        margin-bottom: 1rem;
    }

    .footer-title {
        font-size: 1.3rem;
    }
}

@media (max-width: 576px) {
    .footer-content .container {
        padding: 2rem 1rem;
    }

    .footer-bottom .container {
        padding: 1rem;
    }

    .footer-badges .badge {
        font-size: 0.75rem;
        padding: 0.4rem 0.6rem;
    }
}

/* أنماط بطاقات الإحصائيات */
.stats-card {
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border: none;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(14, 165, 233, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
    color: white;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--card-color, #0ea5e9), var(--card-color-light, #7dd3fc));
    z-index: 1;
}

.stats-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(14, 165, 233, 0.2);
}

.stats-card.primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    --card-color: #3b82f6;
    --card-color-light: #93c5fd;
}

.stats-card.warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    --card-color: #f59e0b;
    --card-color-light: #fbbf24;
}

.stats-card.success {
    background: linear-gradient(135deg, #10b981, #059669);
    --card-color: #10b981;
    --card-color-light: #34d399;
}

.stats-card.danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    --card-color: #ef4444;
    --card-color-light: #f87171;
}

.stats-card.info {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
    --card-color: #06b6d4;
    --card-color-light: #22d3ee;
}

.stats-icon {
    transition: all 0.3s ease;
    filter: drop-shadow(0 2px 8px rgba(0,0,0,0.3));
}

.stats-card:hover .stats-icon {
    transform: scale(1.1) rotate(5deg);
}

.stats-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    line-height: 1;
}

.stats-subtitle {
    font-size: 0.8rem;
    opacity: 0.8;
    font-weight: 400;
}

.stats-footer {
    background: rgba(0,0,0,0.1);
    border-top: 1px solid rgba(255,255,255,0.1);
    transition: all 0.3s ease;
}

.stats-card:hover .stats-footer {
    background: rgba(0,0,0,0.2);
}

.stats-footer a {
    transition: all 0.3s ease;
}

.stats-footer a:hover {
    transform: translateX(5px);
}

/* معلومات المستخدم */
.user-info-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: rgba(14, 165, 233, 0.05);
    border-radius: 10px;
    border-left: 4px solid var(--medical-primary);
    transition: all 0.3s ease;
}

.user-info-item:hover {
    background: rgba(14, 165, 233, 0.1);
    transform: translateX(5px);
}

.user-info-item i {
    margin-left: 0.5rem;
    font-size: 1.1rem;
}

.user-info-item strong {
    margin-left: 0.5rem;
    color: var(--medical-dark);
}

/* تحسينات الاستجابة للبطاقات الإحصائية */
@media (max-width: 768px) {
    .stats-card {
        border-radius: 15px;
        margin-bottom: 1rem;
    }

    .stats-number {
        font-size: 2rem;
    }

    .stats-title {
        font-size: 0.9rem;
    }

    .user-info-item {
        padding: 0.5rem;
        margin-bottom: 0.75rem;
    }
}

@media (max-width: 576px) {
    .stats-card {
        border-radius: 12px;
    }

    .stats-number {
        font-size: 1.8rem;
    }

    .stats-title {
        font-size: 0.85rem;
    }

    .stats-subtitle {
        font-size: 0.75rem;
    }

    .stats-icon i {
        font-size: 2rem !important;
    }
}
