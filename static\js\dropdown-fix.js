// إصلاح شامل للقوائم المنسدلة - ALEMEIS System
(function() {
    'use strict';

    let isInitialized = false;

    // انتظار تحميل DOM
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🔧 بدء إصلاح القوائم المنسدلة...');

        // تأخير قصير للتأكد من تحميل جميع العناصر
        setTimeout(initializeDropdowns, 500);
    });

    function initializeDropdowns() {
        if (isInitialized) return;

        try {
            console.log('🚀 تهيئة القوائم المنسدلة...');

            // إصلاح عرض البيانات أولاً
            fixDataDisplay();

            // تعطيل Bootstrap dropdown تماماً
            disableBootstrapDropdowns();

            // البحث عن جميع القوائم المنسدلة
            const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
            console.log(`📋 تم العثور على ${dropdownToggles.length} قائمة منسدلة`);

            dropdownToggles.forEach((toggle, index) => {
                setupDropdown(toggle, index);
            });

            // إضافة مستمعي الأحداث العامة
            setupGlobalEventListeners();

            isInitialized = true;
            console.log('✅ تم إكمال إصلاح القوائم المنسدلة بنجاح');

        } catch (error) {
            console.error('❌ خطأ في تهيئة القوائم المنسدلة:', error);
        }
    }

    function disableBootstrapDropdowns() {
        // إزالة جميع مستمعي أحداث Bootstrap
        const dropdowns = document.querySelectorAll('[data-bs-toggle="dropdown"]');
        dropdowns.forEach(dropdown => {
            dropdown.removeAttribute('data-bs-toggle');
        });
    }

    function setupDropdown(toggle, index) {
        console.log(`🔧 إعداد القائمة ${index + 1}:`, toggle.textContent.trim());

        // إزالة أي مستمعين موجودين
        const newToggle = toggle.cloneNode(true);
        toggle.parentNode.replaceChild(newToggle, toggle);

        // إضافة مستمع النقر الجديد
        newToggle.addEventListener('click', function(e) {
            handleDropdownClick(e, this);
        });

        // التأكد من وجود القائمة المنسدلة
        const menu = newToggle.nextElementSibling;
        if (menu && menu.classList.contains('dropdown-menu')) {
            // إخفاء القائمة في البداية
            menu.style.display = 'none';
            menu.classList.remove('show');

            // إضافة أنماط أساسية للقائمة
            styleDropdownMenu(menu);

            console.log(`✅ تم إعداد القائمة ${index + 1} بنجاح`);
        } else {
            console.warn(`⚠️ لم يتم العثور على قائمة للزر ${index + 1}`);
        }
    }

    function styleDropdownMenu(menu) {
        // إضافة أنماط أساسية للقائمة المنسدلة
        menu.style.position = 'absolute';
        menu.style.zIndex = '1050';
        menu.style.minWidth = '200px';
        menu.style.padding = '8px 0';
        menu.style.margin = '0';
        menu.style.backgroundColor = '#ffffff';
        menu.style.border = '1px solid rgba(0,0,0,.15)';
        menu.style.borderRadius = '8px';
        menu.style.boxShadow = '0 6px 12px rgba(0,0,0,.175)';
        menu.style.maxHeight = '400px';
        menu.style.overflowY = 'auto';
    }

    function setupGlobalEventListeners() {
        // إضافة مستمع للنقر خارج القوائم
        document.addEventListener('click', handleOutsideClick);

        // إضافة مستمع لمفتاح Escape
        document.addEventListener('keydown', handleEscapeKey);
    }

    function handleDropdownClick(event, toggle) {
        event.preventDefault();
        event.stopPropagation();

        console.log('🖱️ تم النقر على:', toggle.textContent.trim());

        const menu = toggle.nextElementSibling;
        if (!menu || !menu.classList.contains('dropdown-menu')) {
            console.warn('⚠️ لم يتم العثور على قائمة منسدلة');
            return;
        }

        // إغلاق جميع القوائم الأخرى أولاً
        closeAllDropdowns(menu);

        // تبديل حالة القائمة الحالية
        const isVisible = menu.style.display === 'block';

        if (isVisible) {
            closeDropdown(toggle, menu);
            console.log('❌ تم إخفاء القائمة');
        } else {
            openDropdown(toggle, menu);
            console.log('✅ تم إظهار القائمة');
        }
    }

    function openDropdown(toggle, menu) {
        // إظهار القائمة
        menu.style.display = 'block';
        menu.classList.add('show');

        // تحديث خصائص الزر
        toggle.setAttribute('aria-expanded', 'true');
        toggle.classList.add('show');

        // تحديد موقع القائمة
        positionDropdown(toggle, menu);
    }

    function closeDropdown(toggle, menu) {
        // إخفاء القائمة
        menu.style.display = 'none';
        menu.classList.remove('show');

        // تحديث خصائص الزر
        toggle.setAttribute('aria-expanded', 'false');
        toggle.classList.remove('show');
    }

    function closeAllDropdowns(exceptMenu = null) {
        const allMenus = document.querySelectorAll('.dropdown-menu');
        allMenus.forEach(menu => {
            if (menu !== exceptMenu) {
                const toggle = menu.previousElementSibling;
                if (toggle && toggle.classList.contains('dropdown-toggle')) {
                    closeDropdown(toggle, menu);
                }
            }
        });
    }

    function fixDataDisplay() {
        try {
            console.log('🔧 إصلاح عرض البيانات...');

            // إصلاح جميع عناصر الشريط العلوي
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.style.display = 'flex';
                link.style.alignItems = 'center';
                link.style.visibility = 'visible';
                link.style.opacity = '1';

                // إصلاح الأيقونات
                const icons = link.querySelectorAll('i');
                icons.forEach(icon => {
                    icon.style.display = 'inline-block';
                    icon.style.visibility = 'visible';
                    icon.style.opacity = '1';
                    icon.style.marginLeft = '8px';
                });
            });

            // إصلاح عناصر القوائم المنسدلة
            const dropdownItems = document.querySelectorAll('.dropdown-item');
            dropdownItems.forEach(item => {
                item.style.display = 'flex';
                item.style.alignItems = 'center';
                item.style.visibility = 'visible';
                item.style.opacity = '1';
                item.style.padding = '8px 16px';
                item.style.textDecoration = 'none';
                item.style.color = '#212529';

                // إصلاح الأيقونات داخل العناصر
                const icons = item.querySelectorAll('i');
                icons.forEach(icon => {
                    icon.style.marginLeft = '8px';
                    icon.style.width = '16px';
                    icon.style.textAlign = 'center';
                });
            });

            console.log('✅ تم إصلاح عرض البيانات بنجاح');

        } catch (error) {
            console.error('❌ خطأ في إصلاح عرض البيانات:', error);
        }
    }

    function positionDropdown(toggle, menu) {
        try {
            // الحصول على موقع الزر
            const toggleRect = toggle.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const viewportWidth = window.innerWidth;

            // إعادة تعيين الموقع الأساسي
            menu.style.position = 'absolute';
            menu.style.zIndex = '1050';
            menu.style.transform = 'none';
            menu.style.visibility = 'visible';
            menu.style.opacity = '1';

            // حساب الموقع الأفقي
            if (menu.classList.contains('dropdown-menu-end')) {
                // للقوائم في الجانب الأيمن
                menu.style.right = '0';
                menu.style.left = 'auto';
            } else {
                // للقوائم في الجانب الأيسر
                menu.style.left = '0';
                menu.style.right = 'auto';
            }

            // حساب الموقع العمودي
            menu.style.top = '100%';
            menu.style.bottom = 'auto';

            // التأكد من أن القائمة لا تخرج من الشاشة
            setTimeout(() => {
                const menuRect = menu.getBoundingClientRect();

                // تصحيح الموقع الأفقي إذا لزم الأمر
                if (menuRect.right > viewportWidth) {
                    menu.style.left = 'auto';
                    menu.style.right = '0';
                }

                if (menuRect.left < 0) {
                    menu.style.left = '0';
                    menu.style.right = 'auto';
                }

                // تصحيح الموقع العمودي إذا لزم الأمر
                if (menuRect.bottom > viewportHeight) {
                    menu.style.top = 'auto';
                    menu.style.bottom = '100%';
                }

                console.log('📍 تم تحديد موقع القائمة بنجاح');
            }, 10);

        } catch (error) {
            console.error('❌ خطأ في تحديد موقع القائمة:', error);
        }
    }

    function handleOutsideClick(event) {
        // التحقق من أن النقر خارج أي قائمة منسدلة
        if (!event.target.closest('.dropdown')) {
            closeAllDropdowns();
            console.log('🔒 تم إغلاق جميع القوائم');
        }
    }

    function handleEscapeKey(event) {
        if (event.key === 'Escape') {
            closeAllDropdowns();
            console.log('⌨️ تم إغلاق القوائم بمفتاح Escape');
        }
    }

    // تصدير الدوال للاستخدام الخارجي
    window.DropdownFix = {
        init: initializeDropdowns,
        closeAll: closeAllDropdowns,
        reinit: function() {
            isInitialized = false;
            initializeDropdowns();
        }
    };

    console.log('📦 تم تحميل وحدة إصلاح القوائم المنسدلة');
})();
