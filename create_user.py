import sqlite3
import hashlib

# Function to hash password
def hash_password(password):
    return hashlib.sha256(password.encode()).hexdigest()

# Connect to the database
conn = sqlite3.connect('alemis.db')
conn.row_factory = sqlite3.Row
cursor = conn.cursor()

# Create admin user
username = 'admin'
password = 'admin123'
password_hash = hash_password(password)
email = '<EMAIL>'
first_name = 'مدير'
last_name = 'النظام'
role = 'admin'
department_id = 1  # قسم المختبر

# Check if user already exists
cursor.execute("SELECT * FROM users WHERE username = ?", (username,))
if cursor.fetchone():
    print(f"User '{username}' already exists")
else:
    # Insert user
    cursor.execute('''
        INSERT INTO users (username, password_hash, email, first_name, last_name, role, department_id, is_active)
        VALUES (?, ?, ?, ?, ?, ?, ?, 1)
    ''', (username, password_hash, email, first_name, last_name, role, department_id))
    conn.commit()
    print(f"User '{username}' created successfully")

# Create manager user
username = 'manager'
password = 'manager123'
password_hash = hash_password(password)
email = '<EMAIL>'
first_name = 'مدير'
last_name = 'المختبر'
role = 'manager'
department_id = 1  # قسم المختبر

# Check if user already exists
cursor.execute("SELECT * FROM users WHERE username = ?", (username,))
if cursor.fetchone():
    print(f"User '{username}' already exists")
else:
    # Insert user
    cursor.execute('''
        INSERT INTO users (username, password_hash, email, first_name, last_name, role, department_id, is_active)
        VALUES (?, ?, ?, ?, ?, ?, ?, 1)
    ''', (username, password_hash, email, first_name, last_name, role, department_id))
    conn.commit()
    print(f"User '{username}' created successfully")

# Create employee user
username = 'employee'
password = 'employee123'
password_hash = hash_password(password)
email = '<EMAIL>'
first_name = 'موظف'
last_name = 'المختبر'
role = 'employee'
department_id = 1  # قسم المختبر

# Check if user already exists
cursor.execute("SELECT * FROM users WHERE username = ?", (username,))
if cursor.fetchone():
    print(f"User '{username}' already exists")
else:
    # Insert user
    cursor.execute('''
        INSERT INTO users (username, password_hash, email, first_name, last_name, role, department_id, is_active)
        VALUES (?, ?, ?, ?, ?, ?, ?, 1)
    ''', (username, password_hash, email, first_name, last_name, role, department_id))
    conn.commit()
    print(f"User '{username}' created successfully")

# Create HR user
username = 'hr'
password = 'hr123'
password_hash = hash_password(password)
email = '<EMAIL>'
first_name = 'مدير'
last_name = 'الموارد البشرية'
role = 'hr'
department_id = 1  # قسم المختبر

# Check if user already exists
cursor.execute("SELECT * FROM users WHERE username = ?", (username,))
if cursor.fetchone():
    print(f"User '{username}' already exists")
else:
    # Insert user
    cursor.execute('''
        INSERT INTO users (username, password_hash, email, first_name, last_name, role, department_id, is_active)
        VALUES (?, ?, ?, ?, ?, ?, ?, 1)
    ''', (username, password_hash, email, first_name, last_name, role, department_id))
    conn.commit()
    print(f"User '{username}' created successfully")

# Create GM user
username = 'gm'
password = 'gm123'
password_hash = hash_password(password)
email = '<EMAIL>'
first_name = 'المدير'
last_name = 'العام'
role = 'gm'
department_id = 1  # قسم المختبر

# Check if user already exists
cursor.execute("SELECT * FROM users WHERE username = ?", (username,))
if cursor.fetchone():
    print(f"User '{username}' already exists")
else:
    # Insert user
    cursor.execute('''
        INSERT INTO users (username, password_hash, email, first_name, last_name, role, department_id, is_active)
        VALUES (?, ?, ?, ?, ?, ?, ?, 1)
    ''', (username, password_hash, email, first_name, last_name, role, department_id))
    conn.commit()
    print(f"User '{username}' created successfully")

# Close the connection
conn.close()

print("Users created successfully")
