/* إصلاح شامل للنوافذ المنبثقة - ALEMEIS System */

/* إعدادات أساسية للنوافذ المنبثقة */
.modal {
    z-index: 1050 !important;
    display: none !important;
}

.modal.show {
    display: block !important;
}

.modal-backdrop {
    z-index: 1040 !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
}

/* تحسين مظهر النافذة */
.modal-dialog {
    margin: 1.75rem auto !important;
    max-width: 500px !important;
    position: relative !important;
    width: auto !important;
    pointer-events: none !important;
}

.modal-dialog-centered {
    display: flex !important;
    align-items: center !important;
    min-height: calc(100% - 3.5rem) !important;
}

.modal-content {
    position: relative !important;
    display: flex !important;
    flex-direction: column !important;
    width: 100% !important;
    pointer-events: auto !important;
    background-color: #ffffff !important;
    background-clip: padding-box !important;
    border: none !important;
    border-radius: 8px !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;
    outline: 0 !important;
}

/* تحسين رأس النافذة */
.modal-header {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 1.5rem !important;
    border-bottom: 1px solid #e9ecef !important;
    border-top-left-radius: 8px !important;
    border-top-right-radius: 8px !important;
}

.modal-title {
    margin-bottom: 0 !important;
    line-height: 1.5 !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
}

.modal-header .btn-close {
    padding: 0.5rem !important;
    margin: -0.5rem -0.5rem -0.5rem auto !important;
}

/* تحسين محتوى النافذة */
.modal-body {
    position: relative !important;
    flex: 1 1 auto !important;
    padding: 1.5rem !important;
}

/* تحسين ذيل النافذة */
.modal-footer {
    display: flex !important;
    align-items: center !important;
    justify-content: flex-end !important;
    padding: 1rem 1.5rem !important;
    border-top: 1px solid #e9ecef !important;
    border-bottom-right-radius: 8px !important;
    border-bottom-left-radius: 8px !important;
    gap: 0.5rem !important;
}

.modal-footer > * {
    margin: 0 !important;
}

/* تأثيرات الحركة */
.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out !important;
    transform: translate(0, -50px) !important;
}

.modal.show .modal-dialog {
    transform: none !important;
}

/* تأثيرات مخصصة */
.modal-opening .modal-dialog {
    animation: modalSlideIn 0.3s ease-out !important;
}

.modal-closing .modal-dialog {
    animation: modalSlideOut 0.3s ease-in !important;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes modalSlideOut {
    from {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    to {
        opacity: 0;
        transform: translateY(-50px) scale(0.95);
    }
}

/* تحسين النوافذ الكبيرة */
.modal-lg {
    max-width: 800px !important;
}

.modal-xl {
    max-width: 1140px !important;
}

.modal-sm {
    max-width: 300px !important;
}

/* تحسين النوافذ ملء الشاشة */
.modal-fullscreen {
    width: 100vw !important;
    max-width: none !important;
    height: 100% !important;
    margin: 0 !important;
}

.modal-fullscreen .modal-content {
    height: 100% !important;
    border: 0 !important;
    border-radius: 0 !important;
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 576px) {
    .modal-dialog {
        margin: 1rem !important;
        max-width: calc(100% - 2rem) !important;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 1rem !important;
    }
}

/* تحسين ألوان النوافذ الطبية */
.modal-header.bg-primary {
    background: linear-gradient(135deg, #0ea5e9, #0284c7) !important;
    color: white !important;
    border-bottom: none !important;
}

.modal-header.bg-success {
    background: linear-gradient(135deg, #10b981, #059669) !important;
    color: white !important;
    border-bottom: none !important;
}

.modal-header.bg-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706) !important;
    color: white !important;
    border-bottom: none !important;
}

.modal-header.bg-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626) !important;
    color: white !important;
    border-bottom: none !important;
}

/* تحسين أزرار النوافذ */
.modal-footer .btn {
    min-width: 100px !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
}

.modal-footer .btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

.modal-footer .btn:active {
    transform: translateY(0) !important;
}

/* تحسين النماذج داخل النوافذ */
.modal .form-control {
    border-radius: 6px !important;
    border: 1px solid #d1d5db !important;
    transition: all 0.2s ease !important;
}

.modal .form-control:focus {
    border-color: #0ea5e9 !important;
    box-shadow: 0 0 0 0.2rem rgba(14, 165, 233, 0.25) !important;
}

.modal .form-label {
    font-weight: 500 !important;
    color: #374151 !important;
    margin-bottom: 0.5rem !important;
}

/* تحسين الأيقونات في النوافذ */
.modal-header i,
.modal-body i,
.modal-footer i {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
}

/* تحسين النوافذ في الوضع المظلم */
[data-theme="dark"] .modal-content {
    background-color: #374151 !important;
    color: #f3f4f6 !important;
}

[data-theme="dark"] .modal-header {
    border-bottom-color: #4b5563 !important;
}

[data-theme="dark"] .modal-footer {
    border-top-color: #4b5563 !important;
}

[data-theme="dark"] .modal .form-control {
    background-color: #4b5563 !important;
    border-color: #6b7280 !important;
    color: #f3f4f6 !important;
}

[data-theme="dark"] .modal .form-control:focus {
    border-color: #60a5fa !important;
    box-shadow: 0 0 0 0.2rem rgba(96, 165, 250, 0.25) !important;
}

/* إصلاح مشاكل التداخل */
.modal * {
    box-sizing: border-box !important;
}

/* تحسين الأداء */
.modal {
    will-change: transform, opacity !important;
    backface-visibility: hidden !important;
}

/* إصلاح مشاكل الخطوط */
.modal-title,
.modal-body,
.modal-footer {
    font-family: inherit !important;
    direction: rtl !important;
    text-align: right !important;
}

/* تحسين النوافذ الخاصة بالنظام الطبي */
.medical-modal .modal-content {
    border: 2px solid #0ea5e9 !important;
    box-shadow: 0 15px 35px rgba(14, 165, 233, 0.1) !important;
}

.medical-modal .modal-header {
    background: linear-gradient(135deg, #0ea5e9, #0284c7) !important;
    color: white !important;
}

.medical-modal .modal-body {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9) !important;
}

/* تحسين نوافذ التأكيد */
.confirm-modal .modal-dialog {
    max-width: 400px !important;
}

.confirm-modal .modal-body {
    text-align: center !important;
    padding: 2rem !important;
}

.confirm-modal .modal-body i {
    font-size: 3rem !important;
    margin-bottom: 1rem !important;
}

/* تحسين نوافذ النماذج */
.form-modal .modal-dialog {
    max-width: 600px !important;
}

.form-modal .modal-body {
    padding: 2rem !important;
}

/* إصلاح مشاكل الظهور */
.modal.show {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.modal-backdrop.show {
    opacity: 0.5 !important;
}
