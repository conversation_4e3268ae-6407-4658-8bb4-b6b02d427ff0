#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import subprocess

def check_python():
    """فحص إصدار Python"""
    print(f"🐍 إصدار Python: {sys.version}")
    if sys.version_info < (3, 7):
        print("❌ يتطلب Python 3.7 أو أحدث")
        return False
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    try:
        print("📦 تثبيت المتطلبات...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المتطلبات: {e}")
        return False

def check_database():
    """فحص قاعدة البيانات"""
    if not os.path.exists('alemis.db'):
        print("⚠️ قاعدة البيانات غير موجودة. جاري إنشاؤها...")
        try:
            subprocess.check_call([sys.executable, "init_db.py"])
            print("✅ تم إنشاء قاعدة البيانات بنجاح")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ فشل في إنشاء قاعدة البيانات: {e}")
            return False
    else:
        print("✅ قاعدة البيانات موجودة")
        return True

def run_app():
    """تشغيل التطبيق"""
    try:
        print("🚀 بدء تشغيل خادم ALEMEIS...")
        print("📍 الخادم متاح على: http://localhost:5000")
        print("🔑 حسابات الاختبار:")
        print("   - المدير: admin / admin123")
        print("   - مدير المختبر: manager / admin123")
        print("   - الموارد البشرية: hr / admin123")
        print("   - المدير العام: gm / admin123")
        print("   - موظف 1: employee1 / admin123")
        print("   - موظف 2: employee2 / admin123")
        print("=" * 50)
        
        # تشغيل التطبيق
        subprocess.run([sys.executable, "app.py"])
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

def main():
    """الدالة الرئيسية"""
    print("🔧 فحص النظام...")
    
    # فحص Python
    if not check_python():
        return
    
    # فحص قاعدة البيانات
    if not check_database():
        return
    
    # تشغيل التطبيق
    run_app()

if __name__ == '__main__':
    main()
