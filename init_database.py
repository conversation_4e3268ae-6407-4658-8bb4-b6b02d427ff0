import sqlite3
import os

print('إنشاء قاعدة البيانات من ملف schema.sql...')

# حذف قاعدة البيانات الموجودة إذا كانت موجودة
if os.path.exists('alemis.db'):
    os.remove('alemis.db')
    print('تم حذف قاعدة البيانات القديمة')

# قراءة ملف schema.sql
with open('schema.sql', 'r', encoding='utf-8') as f:
    schema_sql = f.read()

# إنشاء قاعدة البيانات الجديدة
conn = sqlite3.connect('alemis.db')
cursor = conn.cursor()

# تنفيذ جميع الأوامر في ملف schema.sql
cursor.executescript(schema_sql)

# حفظ التغييرات
conn.commit()
conn.close()

print('تم إنشاء قاعدة البيانات بنجاح!')
print('')
print('الحسابات المتاحة:')
print('- admin / admin123 (مدير النظام)')
print('- manager / admin123 (مدير المختبر)')
print('- hr / admin123 (موارد بشرية)')
print('- gm / admin123 (مدير عام)')
print('- employee1 / admin123 (موظف)')
print('- employee2 / admin123 (موظف)')
print('')
print('يمكنك الآن تشغيل الموقع بالأمر: python app.py')
