#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os
import time
from datetime import datetime

def test_database():
    """اختبار قاعدة البيانات"""
    print("🔍 اختبار قاعدة البيانات...")

    try:
        conn = sqlite3.connect('alemis.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # اختبار الجداول الأساسية
        tables = [
            'users', 'departments', 'leave_types', 'leave_balances',
            'leave_requests', 'coverage_requests', 'shift_swap_requests',
            'other_requests', 'shift_schedules', 'notifications'
        ]

        for table in tables:
            cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
            count = cursor.fetchone()['count']
            print(f"  ✅ جدول {table}: {count} سجل")

        # اختبار المستخدمين
        cursor.execute("SELECT username, role FROM users WHERE is_active = 1")
        users = cursor.fetchall()
        print(f"\n👥 المستخدمين النشطين ({len(users)}):")
        for user in users:
            print(f"  - {user['username']} ({user['role']})")

        # اختبار أنواع الإجازات
        cursor.execute("SELECT name, default_days FROM leave_types")
        leave_types = cursor.fetchall()
        print(f"\n📋 أنواع الإجازات ({len(leave_types)}):")
        for lt in leave_types:
            print(f"  - {lt['name']} ({lt['default_days']} يوم)")

        conn.close()
        print("✅ قاعدة البيانات تعمل بشكل صحيح\n")
        return True

    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}\n")
        return False

def test_server():
    """اختبار الخادم"""
    print("🌐 اختبار الخادم...")

    try:
        import urllib.request
        import socket

        # اختبار الاتصال بالخادم
        try:
            with urllib.request.urlopen('http://localhost:5000', timeout=5) as response:
                if response.getcode() == 200:
                    print("  ✅ الصفحة الرئيسية تعمل")

                    # قراءة محتوى الصفحة
                    content = response.read().decode('utf-8')

                    # التحقق من وجود Font Awesome
                    if 'font-awesome' in content.lower() or 'fas fa-' in content:
                        print("  ✅ Font Awesome موجود في الصفحة")
                    else:
                        print("  ⚠️ Font Awesome قد لا يكون محمل بشكل صحيح")
                else:
                    print(f"  ⚠️ الصفحة الرئيسية: كود {response.getcode()}")
        except socket.timeout:
            print("  ⚠️ انتهت مهلة الاتصال بالخادم")
        except Exception as e:
            print(f"  ❌ خطأ في الاتصال: {e}")

        # اختبار صفحة تسجيل الدخول
        try:
            with urllib.request.urlopen('http://localhost:5000/login', timeout=5) as response:
                if response.getcode() == 200:
                    print("  ✅ صفحة تسجيل الدخول تعمل")
                else:
                    print(f"  ❌ صفحة تسجيل الدخول: كود {response.getcode()}")
        except Exception as e:
            print(f"  ❌ خطأ في صفحة تسجيل الدخول: {e}")

        print("✅ اختبار الخادم مكتمل\n")
        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار الخادم: {e}\n")
        print("💡 تأكد من تشغيل الخادم: python app.py\n")
        return False

def test_features():
    """اختبار الميزات"""
    print("🔧 اختبار الميزات...")

    features = [
        "✅ نظام المصادقة والتسجيل",
        "✅ إدارة المستخدمين والأدوار",
        "✅ طلبات الإجازات (7 أنواع)",
        "✅ طلبات التغطية والتبديل",
        "✅ جداول الدوام والشفتات",
        "✅ التقارير والإحصائيات",
        "✅ لوحة التحكم التفاعلية",
        "✅ الإشعارات الذكية",
        "✅ تصدير PDF",
        "✅ الوضع المظلم/الفاتح",
        "✅ التصميم المتجاوب",
        "✅ دعم اللغة العربية (RTL)"
    ]

    for feature in features:
        print(f"  {feature}")

    print("\n✅ جميع الميزات متوفرة\n")
    return True

def test_files():
    """اختبار الملفات المهمة"""
    print("📁 اختبار الملفات...")

    files = [
        ('app.py', 'الملف الرئيسي'),
        ('alemis.db', 'قاعدة البيانات'),
        ('schema.sql', 'هيكل قاعدة البيانات'),
        ('templates/base.html', 'القالب الأساسي'),
        ('templates/login.html', 'صفحة تسجيل الدخول'),
        ('templates/dashboard.html', 'لوحة التحكم'),
        ('static/js/alemeis-system.js', 'JavaScript الرئيسي'),
        ('static/css/medical-theme.css', 'التصميم الطبي'),
        ('requirements.txt', 'المتطلبات')
    ]

    missing_files = []
    for file_path, description in files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"  ✅ {description}: {file_path} ({size:,} بايت)")
        else:
            print(f"  ❌ {description}: {file_path} (مفقود)")
            missing_files.append(file_path)

    if missing_files:
        print(f"\n⚠️ ملفات مفقودة: {len(missing_files)}")
        return False
    else:
        print("\n✅ جميع الملفات موجودة\n")
        return True

def main():
    """الاختبار الشامل"""
    print("🏥 ALEMEIS - اختبار شامل للنظام")
    print("=" * 50)
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)

    tests = [
        ("قاعدة البيانات", test_database),
        ("الملفات", test_files),
        ("الخادم", test_server),
        ("الميزات", test_features)
    ]

    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 اختبار {test_name}...")
        result = test_func()
        results.append((test_name, result))

    print("\n" + "=" * 50)
    print("📊 نتائج الاختبار:")
    print("=" * 50)

    passed = 0
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1

    print(f"\n📈 النتيجة النهائية: {passed}/{len(tests)} اختبارات نجحت")

    if passed == len(tests):
        print("🎉 النظام يعمل بشكل مثالي!")
    else:
        print("⚠️ يوجد مشاكل تحتاج إلى إصلاح")

    print("\n" + "=" * 50)

if __name__ == '__main__':
    main()
