#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from datetime import datetime

def add_test_coverage_balance():
    """إضافة رصيد تجريبي لبدل التغطية لموظف للاختبار"""
    
    # Connect to database
    conn = sqlite3.connect('alemis.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        print("🧪 إضافة رصيد تجريبي لبدل التغطية...")
        print("=" * 50)
        
        # Get coverage leave type
        cursor.execute('SELECT * FROM leave_types WHERE name = ?', ('بدل يوم تغطية',))
        coverage_leave_type = cursor.fetchone()
        
        if not coverage_leave_type:
            print("❌ نوع إجازة 'بدل يوم تغطية' غير موجود!")
            return
            
        leave_type_id = coverage_leave_type['id']
        current_year = datetime.now().year
        
        # Get first employee
        cursor.execute('SELECT * FROM users WHERE role = "employee" LIMIT 1')
        employee = cursor.fetchone()
        
        if not employee:
            print("❌ لا يوجد موظفون في النظام!")
            return
            
        user_id = employee['id']
        print(f"👤 الموظف: {employee['first_name']} {employee['last_name']} (ID: {user_id})")
        
        # Check current balance
        cursor.execute('''
            SELECT * FROM leave_balances 
            WHERE user_id = ? AND leave_type_id = ? AND year = ?
        ''', (user_id, leave_type_id, current_year))
        current_balance = cursor.fetchone()
        
        if current_balance:
            print(f"📊 الرصيد الحالي: إجمالي={current_balance['total_days']}, متبقي={current_balance['remaining_days']}")
            
            # Add 3 days to the balance
            new_total = current_balance['total_days'] + 3
            new_remaining = current_balance['remaining_days'] + 3
            
            cursor.execute('''
                UPDATE leave_balances 
                SET total_days = ?, remaining_days = ?
                WHERE user_id = ? AND leave_type_id = ? AND year = ?
            ''', (new_total, new_remaining, user_id, leave_type_id, current_year))
            
            print(f"✅ تم إضافة 3 أيام بدل تغطية")
            print(f"📈 الرصيد الجديد: إجمالي={new_total}, متبقي={new_remaining}")
            
        else:
            # Create new balance with 3 days
            cursor.execute('''
                INSERT INTO leave_balances (user_id, leave_type_id, year, total_days, used_days, remaining_days)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (user_id, leave_type_id, current_year, 3, 0, 3))
            
            print(f"✅ تم إنشاء رصيد جديد بـ 3 أيام بدل تغطية")
            
        conn.commit()
        
        # Verify the update
        cursor.execute('''
            SELECT * FROM leave_balances 
            WHERE user_id = ? AND leave_type_id = ? AND year = ?
        ''', (user_id, leave_type_id, current_year))
        updated_balance = cursor.fetchone()
        
        if updated_balance:
            print(f"\n🔍 التحقق من التحديث:")
            print(f"   - إجمالي الأيام: {updated_balance['total_days']}")
            print(f"   - الأيام المستخدمة: {updated_balance['used_days']}")
            print(f"   - الأيام المتبقية: {updated_balance['remaining_days']}")
        
        print(f"\n✅ تم تحديث رصيد بدل التغطية بنجاح!")
        print(f"💡 يمكنك الآن فتح صفحة أرصدة الإجازات لرؤية التحديث")
        
    except Exception as e:
        print(f'❌ خطأ: {e}')
        conn.rollback()
    finally:
        conn.close()

if __name__ == '__main__':
    add_test_coverage_balance()
