// إصلاح موقع القوائم المنسدلة - ALEMEIS System
(function() {
    'use strict';

    // تشغيل الإصلاح عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🔧 Starting dropdown position fix...');

        // انتظار تحميل Bootstrap
        setTimeout(initializeDropdownPositioning, 1000);

        // إعادة تطبيق الإصلاح عند تغيير حجم النافذة
        window.addEventListener('resize', debounce(initializeDropdownPositioning, 250));
    });

    function initializeDropdownPositioning() {
        try {
            console.log('🔧 Initializing dropdown positioning...');

            // البحث عن جميع القوائم المنسدلة في الشريط العلوي
            const navbarDropdowns = document.querySelectorAll('.navbar .dropdown');

            navbarDropdowns.forEach((dropdown, index) => {
                const toggle = dropdown.querySelector('.dropdown-toggle');
                const menu = dropdown.querySelector('.dropdown-menu');

                if (toggle && menu) {
                    console.log(`🔧 Setting up dropdown ${index + 1}`);

                    // إزالة أي مستمعين موجودين
                    toggle.removeEventListener('click', handleDropdownToggle);

                    // إضافة مستمع جديد
                    toggle.addEventListener('click', function(e) {
                        handleDropdownToggle(e, toggle, menu);
                    });

                    // إعداد القائمة
                    setupDropdownMenu(menu);
                }
            });

            // إضافة مستمع للنقر خارج القوائم
            document.addEventListener('click', handleOutsideClick);

            console.log('✅ Dropdown positioning initialized');

        } catch (error) {
            console.error('❌ Error initializing dropdown positioning:', error);
        }
    }

    function handleDropdownToggle(event, toggle, menu) {
        event.preventDefault();
        event.stopPropagation();

        console.log('🖱️ Dropdown toggle clicked');

        // إغلاق جميع القوائم الأخرى
        closeAllDropdowns(menu);

        // تبديل حالة القائمة الحالية
        const isOpen = menu.classList.contains('show');

        if (isOpen) {
            closeDropdown(menu);
        } else {
            openDropdown(toggle, menu);
        }
    }

    function openDropdown(toggle, menu) {
        console.log('📂 Opening dropdown');

        // إظهار القائمة
        menu.classList.add('show');
        menu.style.display = 'block';

        // حساب وتطبيق الموقع الصحيح
        positionDropdown(toggle, menu);

        // تحديث خصائص الزر
        toggle.setAttribute('aria-expanded', 'true');
    }

    function closeDropdown(menu) {
        console.log('📁 Closing dropdown with animation');

        // إضافة تأثير الإغلاق
        menu.classList.add('hiding');
        menu.classList.remove('show');

        // إخفاء القائمة بعد انتهاء التأثير
        setTimeout(() => {
            menu.style.display = 'none';
            menu.classList.remove('hiding');

            // إزالة السهم
            const arrow = menu.querySelector('.dropdown-arrow');
            if (arrow) {
                arrow.remove();
            }
        }, 150);

        // تحديث خصائص الزر
        const toggle = menu.previousElementSibling;
        if (toggle) {
            toggle.setAttribute('aria-expanded', 'false');
        }
    }

    function positionDropdown(toggle, menu) {
        try {
            console.log('📍 Positioning dropdown directly under icon...');

            // الحصول على أبعاد ومواقع العناصر
            const toggleRect = toggle.getBoundingClientRect();
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;

            // إعداد القائمة للموقع الثابت
            menu.style.position = 'fixed';
            menu.style.zIndex = '9999';
            menu.style.transform = 'none';
            menu.style.visibility = 'visible';
            menu.style.opacity = '1';

            // حساب الموقع الأفقي - مباشرة تحت الأيقونة
            const iconCenterX = toggleRect.left + (toggleRect.width / 2);
            const menuWidth = 220; // عرض القائمة المتوقع
            let leftPosition = iconCenterX - (menuWidth / 2); // توسيط القائمة تحت الأيقونة

            // التأكد من عدم خروج القائمة من الشاشة
            if (leftPosition < 10) {
                leftPosition = 10; // مسافة من الحافة اليسرى
            } else if (leftPosition + menuWidth > viewportWidth - 10) {
                leftPosition = viewportWidth - menuWidth - 10; // مسافة من الحافة اليمنى
            }

            menu.style.left = leftPosition + 'px';
            menu.style.right = 'auto';

            // حساب الموقع العمودي - مباشرة تحت الأيقونة
            const topPosition = toggleRect.bottom + 8; // 8px مسافة من الأيقونة
            const spaceBelow = viewportHeight - toggleRect.bottom;
            const menuHeight = 250; // ارتفاع القائمة المتوقع

            if (spaceBelow > menuHeight + 20) {
                // عرض القائمة تحت الأيقونة
                menu.style.top = topPosition + 'px';
                menu.style.bottom = 'auto';
            } else {
                // عرض القائمة فوق الأيقونة
                menu.style.bottom = (viewportHeight - toggleRect.top + 8) + 'px';
                menu.style.top = 'auto';
            }

            // إضافة سهم يشير للأيقونة
            addDropdownArrow(menu, iconCenterX, leftPosition);

            // تطبيق تأثير الظهور
            menu.style.animation = 'dropdownFadeIn 0.2s ease-out';

            console.log('✅ Dropdown positioned directly under icon at:', {
                iconCenter: iconCenterX,
                menuLeft: leftPosition,
                menuTop: menu.style.top,
                menuBottom: menu.style.bottom
            });

        } catch (error) {
            console.error('❌ Error positioning dropdown:', error);
        }
    }

    function addDropdownArrow(menu, iconCenterX, menuLeft) {
        try {
            // إزالة أي سهم موجود مسبقاً
            const existingArrow = menu.querySelector('.dropdown-arrow');
            if (existingArrow) {
                existingArrow.remove();
            }

            // إنشاء سهم جديد
            const arrow = document.createElement('div');
            arrow.className = 'dropdown-arrow';

            // حساب موقع السهم نسبة للقائمة
            const arrowLeft = iconCenterX - menuLeft - 8; // 8px نصف عرض السهم

            arrow.style.cssText = `
                position: absolute;
                top: -8px;
                left: ${arrowLeft}px;
                width: 16px;
                height: 16px;
                background: white;
                border: 1px solid rgba(0,0,0,0.15);
                border-bottom: none;
                border-right: none;
                transform: rotate(45deg);
                z-index: 1;
            `;

            // إضافة السهم للقائمة
            menu.style.position = 'relative';
            menu.insertBefore(arrow, menu.firstChild);

            console.log('✅ Arrow added at position:', arrowLeft);

        } catch (error) {
            console.error('❌ Error adding dropdown arrow:', error);
        }
    }

    function setupDropdownMenu(menu) {
        // إعداد أساسي للقائمة
        menu.style.minWidth = '200px';
        menu.style.maxWidth = '300px';
        menu.style.backgroundColor = '#ffffff';
        menu.style.border = '1px solid rgba(0,0,0,0.15)';
        menu.style.borderRadius = '8px';
        menu.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
        menu.style.padding = '8px 0';
        menu.style.margin = '0';
    }

    function closeAllDropdowns(exceptMenu = null) {
        const allMenus = document.querySelectorAll('.navbar .dropdown-menu');

        allMenus.forEach(menu => {
            if (menu !== exceptMenu && menu.classList.contains('show')) {
                closeDropdown(menu);
            }
        });
    }

    function handleOutsideClick(event) {
        // التحقق من أن النقر خارج أي قائمة منسدلة
        if (!event.target.closest('.navbar .dropdown')) {
            closeAllDropdowns();
        }
    }

    // دالة مساعدة لتأخير التنفيذ
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // إضافة CSS للتأثيرات
    const style = document.createElement('style');
    style.textContent = `
        @keyframes dropdownFadeIn {
            from {
                opacity: 0;
                transform: translateY(-15px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes dropdownFadeOut {
            from {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
            to {
                opacity: 0;
                transform: translateY(-10px) scale(0.95);
            }
        }

        .navbar .dropdown-menu {
            animation-fill-mode: both;
            transform-origin: top center;
        }

        .navbar .dropdown-menu.show {
            animation: dropdownFadeIn 0.2s ease-out;
        }

        .navbar .dropdown-menu.hiding {
            animation: dropdownFadeOut 0.15s ease-in;
        }

        /* تحسين السهم */
        .dropdown-arrow {
            animation: dropdownFadeIn 0.2s ease-out 0.1s both;
        }
    `;
    document.head.appendChild(style);

    // تصدير الدوال للاستخدام الخارجي
    window.DropdownPositionFix = {
        init: initializeDropdownPositioning,
        closeAll: closeAllDropdowns
    };

    console.log('📦 Dropdown position fix module loaded');
})();
