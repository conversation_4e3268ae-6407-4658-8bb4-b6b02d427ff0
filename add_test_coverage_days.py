#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from datetime import datetime, timedelta

def add_test_coverage_days():
    """إضافة أيام تغطية تجريبية للاختبار"""
    
    # Connect to database
    conn = sqlite3.connect('alemis.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        print("🧪 إضافة أيام تغطية تجريبية للاختبار...")
        print("=" * 50)
        
        # Get first employee
        cursor.execute('SELECT * FROM users WHERE role = "employee" LIMIT 1')
        employee = cursor.fetchone()
        
        if not employee:
            print("❌ لا يوجد موظفون في النظام!")
            return
            
        user_id = employee['id']
        print(f"👤 الموظف: {employee['first_name']} {employee['last_name']} (ID: {user_id})")
        
        # Add some coverage days for testing
        coverage_days_data = [
            {
                'date': (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'),
                'shift_type': 'morning',
                'status': 'approved'
            },
            {
                'date': (datetime.now() - timedelta(days=20)).strftime('%Y-%m-%d'),
                'shift_type': 'evening',
                'status': 'approved'
            },
            {
                'date': (datetime.now() - timedelta(days=10)).strftime('%Y-%m-%d'),
                'shift_type': 'night',
                'status': 'approved'
            }
        ]
        
        added_count = 0
        for coverage_data in coverage_days_data:
            # Check if coverage day already exists
            cursor.execute('''
                SELECT * FROM coverage_days 
                WHERE user_id = ? AND coverage_date = ? AND shift_type = ?
            ''', (user_id, coverage_data['date'], coverage_data['shift_type']))
            
            existing = cursor.fetchone()
            
            if not existing:
                # Add coverage day
                cursor.execute('''
                    INSERT INTO coverage_days (user_id, coverage_date, shift_type, status, created_at)
                    VALUES (?, ?, ?, ?, ?)
                ''', (user_id, coverage_data['date'], coverage_data['shift_type'], 
                      coverage_data['status'], datetime.now()))
                
                print(f"✅ تم إضافة يوم تغطية: {coverage_data['date']} - {coverage_data['shift_type']}")
                added_count += 1
                
                # Add coverage compensation day to balance
                cursor.execute('SELECT * FROM leave_types WHERE name = ?', ('بدل يوم تغطية',))
                coverage_leave_type = cursor.fetchone()
                
                if coverage_leave_type:
                    leave_type_id = coverage_leave_type['id']
                    current_year = datetime.now().year
                    
                    # Get current balance
                    cursor.execute('''
                        SELECT * FROM leave_balances 
                        WHERE user_id = ? AND leave_type_id = ? AND year = ?
                    ''', (user_id, leave_type_id, current_year))
                    
                    balance = cursor.fetchone()
                    
                    if balance:
                        # Update existing balance
                        new_total = balance['total_days'] + 1
                        new_remaining = balance['remaining_days'] + 1
                        
                        cursor.execute('''
                            UPDATE leave_balances 
                            SET total_days = ?, remaining_days = ?
                            WHERE user_id = ? AND leave_type_id = ? AND year = ?
                        ''', (new_total, new_remaining, user_id, leave_type_id, current_year))
                        
                        print(f"   💰 تم إضافة يوم واحد إلى رصيد بدل التغطية (الرصيد الجديد: {new_remaining})")
                    else:
                        # Create new balance
                        cursor.execute('''
                            INSERT INTO leave_balances (user_id, leave_type_id, year, total_days, used_days, remaining_days)
                            VALUES (?, ?, ?, ?, ?, ?)
                        ''', (user_id, leave_type_id, current_year, 1, 0, 1))
                        
                        print(f"   💰 تم إنشاء رصيد جديد بدل التغطية: 1 يوم")
            else:
                print(f"⚠️ يوم التغطية موجود بالفعل: {coverage_data['date']} - {coverage_data['shift_type']}")
        
        conn.commit()
        
        if added_count > 0:
            print(f"\n✅ تم إضافة {added_count} يوم تغطية جديد")
        else:
            print(f"\nℹ️ جميع أيام التغطية موجودة بالفعل")
        
        # Show current coverage days for the user
        cursor.execute('''
            SELECT * FROM coverage_days 
            WHERE user_id = ? 
            ORDER BY coverage_date DESC
        ''', (user_id,))
        
        coverage_days = cursor.fetchall()
        
        print(f"\n📋 أيام التغطية الحالية للموظف ({len(coverage_days)}):")
        for cd in coverage_days:
            shift_ar = {'morning': 'صباحي', 'evening': 'مسائي', 'night': 'ليلي'}.get(cd['shift_type'], cd['shift_type'])
            print(f"  - {cd['coverage_date']} - {shift_ar} ({cd['status']})")
        
        # Show current balance
        cursor.execute('SELECT * FROM leave_types WHERE name = ?', ('بدل يوم تغطية',))
        coverage_leave_type = cursor.fetchone()
        
        if coverage_leave_type:
            cursor.execute('''
                SELECT * FROM leave_balances 
                WHERE user_id = ? AND leave_type_id = ? AND year = ?
            ''', (user_id, coverage_leave_type['id'], datetime.now().year))
            
            balance = cursor.fetchone()
            
            if balance:
                print(f"\n💰 رصيد بدل التغطية الحالي:")
                print(f"   - إجمالي: {balance['total_days']} يوم")
                print(f"   - مستخدم: {balance['used_days']} يوم")
                print(f"   - متبقي: {balance['remaining_days']} يوم")
        
        print(f"\n✅ يمكنك الآن اختبار طلب إجازة بدل يوم تغطية!")
        
    except Exception as e:
        print(f'❌ خطأ: {e}')
        conn.rollback()
    finally:
        conn.close()

if __name__ == '__main__':
    add_test_coverage_days()
