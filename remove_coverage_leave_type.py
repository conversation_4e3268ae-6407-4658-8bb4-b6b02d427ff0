#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from datetime import datetime

def remove_coverage_leave_type():
    """حذف نوع إجازة 'إجازة تغطية' من قاعدة البيانات"""
    
    # Connect to database
    conn = sqlite3.connect('alemis.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        print("🗑️ حذف نوع إجازة 'إجازة تغطية'...")
        print("=" * 50)
        
        # Check if 'إجازة تغطية' leave type exists
        cursor.execute('SELECT * FROM leave_types WHERE name = ?', ('إجازة تغطية',))
        coverage_leave_type = cursor.fetchone()
        
        if coverage_leave_type:
            leave_type_id = coverage_leave_type['id']
            print(f"✅ تم العثور على نوع إجازة 'إجازة تغطية' (ID: {leave_type_id})")
            
            # Check if there are any leave requests using this type
            cursor.execute('SELECT COUNT(*) as count FROM leave_requests WHERE leave_type_id = ?', (leave_type_id,))
            request_count = cursor.fetchone()['count']
            
            if request_count > 0:
                print(f"⚠️ يوجد {request_count} طلب إجازة يستخدم هذا النوع")
                print("سيتم حذف الطلبات أولاً...")
                
                # Delete leave requests
                cursor.execute('DELETE FROM leave_requests WHERE leave_type_id = ?', (leave_type_id,))
                print(f"✅ تم حذف {request_count} طلب إجازة")
            
            # Check if there are any leave balances using this type
            cursor.execute('SELECT COUNT(*) as count FROM leave_balances WHERE leave_type_id = ?', (leave_type_id,))
            balance_count = cursor.fetchone()['count']
            
            if balance_count > 0:
                print(f"⚠️ يوجد {balance_count} رصيد إجازة يستخدم هذا النوع")
                print("سيتم حذف الأرصدة أولاً...")
                
                # Delete leave balances
                cursor.execute('DELETE FROM leave_balances WHERE leave_type_id = ?', (leave_type_id,))
                print(f"✅ تم حذف {balance_count} رصيد إجازة")
            
            # Delete the leave type
            cursor.execute('DELETE FROM leave_types WHERE id = ?', (leave_type_id,))
            print(f"✅ تم حذف نوع إجازة 'إجازة تغطية'")
            
            conn.commit()
            
        else:
            print("ℹ️ نوع إجازة 'إجازة تغطية' غير موجود")
            
        # Show current leave types
        cursor.execute('SELECT * FROM leave_types ORDER BY id')
        leave_types = cursor.fetchall()
        
        print(f"\n📋 أنواع الإجازات المتبقية ({len(leave_types)}):")
        for lt in leave_types:
            print(f"  - {lt['name']} (ID: {lt['id']}, الأيام الافتراضية: {lt['default_days']})")
            
    except Exception as e:
        print(f'❌ خطأ: {e}')
        conn.rollback()
    finally:
        conn.close()

if __name__ == '__main__':
    remove_coverage_leave_type()
