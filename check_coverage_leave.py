#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from datetime import datetime

def check_coverage_leave_status():
    """فحص حالة نوع إجازة بدل يوم تغطية والأرصدة"""
    
    # Connect to database
    conn = sqlite3.connect('alemis.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        print("🔍 فحص حالة نوع إجازة بدل يوم تغطية...")
        print("=" * 60)
        
        # Check if 'بدل يوم تغطية' leave type exists
        cursor.execute('SELECT * FROM leave_types WHERE name = ?', ('بدل يوم تغطية',))
        coverage_leave_type = cursor.fetchone()
        
        if coverage_leave_type:
            print(f"✅ نوع إجازة 'بدل يوم تغطية' موجود:")
            print(f"   - ID: {coverage_leave_type['id']}")
            print(f"   - الاسم: {coverage_leave_type['name']}")
            print(f"   - الوصف: {coverage_leave_type['description']}")
            print(f"   - الأيام الافتراضية: {coverage_leave_type['default_days']}")
            
            # Check balances for this leave type
            current_year = datetime.now().year
            cursor.execute('''
                SELECT COUNT(*) as count FROM leave_balances 
                WHERE leave_type_id = ? AND year = ?
            ''', (coverage_leave_type['id'], current_year))
            balance_count = cursor.fetchone()['count']
            
            print(f"\n📊 أرصدة بدل يوم تغطية للعام {current_year}:")
            print(f"   - عدد الأرصدة الموجودة: {balance_count}")
            
            # Get sample balances
            cursor.execute('''
                SELECT u.first_name, u.last_name, lb.total_days, lb.used_days, lb.remaining_days
                FROM leave_balances lb
                JOIN users u ON lb.user_id = u.id
                WHERE lb.leave_type_id = ? AND lb.year = ?
                LIMIT 5
            ''', (coverage_leave_type['id'], current_year))
            sample_balances = cursor.fetchall()
            
            if sample_balances:
                print("\n   عينة من الأرصدة:")
                for balance in sample_balances:
                    print(f"   - {balance['first_name']} {balance['last_name']}: "
                          f"إجمالي={balance['total_days']}, "
                          f"مستخدم={balance['used_days']}, "
                          f"متبقي={balance['remaining_days']}")
            else:
                print("   ⚠️ لا توجد أرصدة!")
                
        else:
            print("❌ نوع إجازة 'بدل يوم تغطية' غير موجود!")
            
        # Show all leave types
        cursor.execute('SELECT * FROM leave_types ORDER BY id')
        all_leave_types = cursor.fetchall()
        
        print(f"\n📋 جميع أنواع الإجازات ({len(all_leave_types)}):")
        for lt in all_leave_types:
            print(f"   - {lt['name']} (ID: {lt['id']}, الأيام الافتراضية: {lt['default_days']})")
            
        # Check total users
        cursor.execute('SELECT COUNT(*) as count FROM users WHERE role = "employee"')
        employee_count = cursor.fetchone()['count']
        print(f"\n👥 عدد الموظفين: {employee_count}")
        
        # Check if any user has coverage compensation balance > 0
        if coverage_leave_type:
            cursor.execute('''
                SELECT u.first_name, u.last_name, lb.total_days, lb.remaining_days
                FROM leave_balances lb
                JOIN users u ON lb.user_id = u.id
                WHERE lb.leave_type_id = ? AND lb.year = ? AND lb.total_days > 0
            ''', (coverage_leave_type['id'], current_year))
            positive_balances = cursor.fetchall()
            
            if positive_balances:
                print(f"\n💰 موظفون لديهم رصيد بدل تغطية ({len(positive_balances)}):")
                for balance in positive_balances:
                    print(f"   - {balance['first_name']} {balance['last_name']}: "
                          f"إجمالي={balance['total_days']}, متبقي={balance['remaining_days']}")
            else:
                print("\n💰 لا يوجد موظفون لديهم رصيد بدل تغطية حالياً")
                
    except Exception as e:
        print(f'❌ خطأ: {e}')
    finally:
        conn.close()

if __name__ == '__main__':
    check_coverage_leave_status()
