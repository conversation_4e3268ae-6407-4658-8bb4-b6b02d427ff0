#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def create_database():
    """إنشاء قاعدة البيانات من ملف schema.sql"""
    
    print("🗄️ إنشاء قاعدة البيانات...")
    
    # حذف قاعدة البيانات الموجودة إذا كانت موجودة
    if os.path.exists('employees.db'):
        os.remove('employees.db')
        print("✅ تم حذف قاعدة البيانات القديمة")
    
    # قراءة ملف schema.sql
    with open('schema.sql', 'r', encoding='utf-8') as f:
        schema_sql = f.read()
    
    # إنشاء قاعدة البيانات الجديدة
    conn = sqlite3.connect('employees.db')
    cursor = conn.cursor()
    
    # تنفيذ جميع الأوامر في ملف schema.sql
    cursor.executescript(schema_sql)
    
    # حفظ التغييرات
    conn.commit()
    conn.close()
    
    print("✅ تم إنشاء قاعدة البيانات بنجاح!")

if __name__ == '__main__':
    create_database()
