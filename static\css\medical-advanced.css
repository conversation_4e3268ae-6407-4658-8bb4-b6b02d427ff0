/* ملف التحسينات المتقدمة للتصميم الطبي */

/* تحسينات الخطوط والنصوص */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

:root {
    --medical-font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --medical-shadow-glow: 0 0 20px rgba(14, 165, 233, 0.3);
    --medical-shadow-deep: 0 10px 40px rgba(0, 0, 0, 0.1);
    --medical-border-radius-xl: 20px;
    --medical-transition-smooth: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تحسين الخطوط */
body, .medical-card, .medical-table, .medical-btn {
    font-family: var(--medical-font-family);
}

/* تأثيرات الإضاءة المتقدمة */
.medical-glow {
    position: relative;
    overflow: hidden;
}

.medical-glow::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(14, 165, 233, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.5s ease;
    pointer-events: none;
    z-index: 1;
}

.medical-glow:hover::before {
    opacity: 1;
}

/* تأثيرات الجسيمات الطبية */
.medical-particles {
    position: relative;
    overflow: hidden;
}

.medical-particles::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cg fill='rgba(14,165,233,0.1)'%3E%3Ccircle cx='20' cy='20' r='2'%3E%3Canimate attributeName='opacity' values='0;1;0' dur='3s' repeatCount='indefinite'/%3E%3C/circle%3E%3Ccircle cx='80' cy='30' r='1.5'%3E%3Canimate attributeName='opacity' values='0;1;0' dur='2s' repeatCount='indefinite' begin='0.5s'/%3E%3C/circle%3E%3Ccircle cx='40' cy='70' r='1'%3E%3Canimate attributeName='opacity' values='0;1;0' dur='2.5s' repeatCount='indefinite' begin='1s'/%3E%3C/circle%3E%3Ccircle cx='70' cy='80' r='2'%3E%3Canimate attributeName='opacity' values='0;1;0' dur='3.5s' repeatCount='indefinite' begin='1.5s'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E") repeat;
    background-size: 100px 100px;
    opacity: 0.6;
    pointer-events: none;
    z-index: 1;
}

/* تحسينات البطاقات المتقدمة */
.medical-card-premium {
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid rgba(14, 165, 233, 0.1);
    border-radius: var(--medical-border-radius-xl);
    box-shadow: var(--medical-shadow-deep);
    position: relative;
    overflow: hidden;
    transition: var(--medical-transition-smooth);
}

.medical-card-premium::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #0ea5e9, #06b6d4, #0891b2, #0e7490);
    background-size: 300% 100%;
    animation: medicalGradientMove 3s ease-in-out infinite;
}

@keyframes medicalGradientMove {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.medical-card-premium:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--medical-shadow-glow), var(--medical-shadow-deep);
}

/* تحسينات الجداول المتقدمة */
.medical-table-advanced {
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border-radius: var(--medical-border-radius-xl);
    overflow: hidden;
    box-shadow: var(--medical-shadow-deep);
    position: relative;
}

.medical-table-advanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(14, 165, 233, 0.02) 50%, transparent 70%);
    pointer-events: none;
    z-index: 1;
}

.medical-table-row-advanced {
    position: relative;
    transition: var(--medical-transition-smooth);
    z-index: 2;
}

.medical-table-row-advanced:hover {
    background: linear-gradient(90deg, rgba(14, 165, 233, 0.05) 0%, rgba(14, 165, 233, 0.1) 50%, rgba(14, 165, 233, 0.05) 100%);
    transform: translateX(5px);
    box-shadow: 0 4px 20px rgba(14, 165, 233, 0.15);
}

/* تأثيرات الأزرار المتقدمة */
.medical-btn-advanced {
    background: linear-gradient(145deg, #0ea5e9, #0284c7);
    border: none;
    border-radius: 25px;
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    position: relative;
    overflow: hidden;
    transition: var(--medical-transition-smooth);
    box-shadow: 0 4px 15px rgba(14, 165, 233, 0.3);
}

.medical-btn-advanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.medical-btn-advanced:hover::before {
    left: 100%;
}

.medical-btn-advanced:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(14, 165, 233, 0.4);
}

.medical-btn-advanced:active {
    transform: translateY(-1px) scale(1.02);
}

/* تأثيرات الشارات المتقدمة */
.medical-badge-advanced {
    background: linear-gradient(145deg, #0ea5e9, #0284c7);
    color: white;
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    font-size: 0.85rem;
    position: relative;
    overflow: hidden;
    transition: var(--medical-transition-smooth);
    box-shadow: 0 2px 10px rgba(14, 165, 233, 0.3);
}

.medical-badge-advanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.medical-badge-advanced:hover::before {
    transform: translateX(100%);
}

.medical-badge-advanced:hover {
    transform: scale(1.1) rotate(2deg);
    box-shadow: 0 4px 20px rgba(14, 165, 233, 0.4);
}

/* تأثيرات الأيقونات المتقدمة */
.medical-icon-advanced {
    position: relative;
    display: inline-block;
    transition: var(--medical-transition-smooth);
}

.medical-icon-advanced::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(14, 165, 233, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.4s ease;
    z-index: -1;
}

.medical-icon-advanced:hover::before {
    width: 40px;
    height: 40px;
}

.medical-icon-advanced:hover {
    transform: scale(1.2) rotate(10deg);
    color: #0ea5e9;
}

/* تأثيرات التحميل المتقدمة */
.medical-loading {
    position: relative;
    overflow: hidden;
}

.medical-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(14, 165, 233, 0.3), transparent);
    animation: medicalLoading 2s infinite;
}

@keyframes medicalLoading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* تأثيرات النبض الطبي */
.medical-pulse {
    animation: medicalPulse 2s ease-in-out infinite;
}

@keyframes medicalPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(14, 165, 233, 0.7);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(14, 165, 233, 0);
    }
}

/* تأثيرات الموجات الطبية */
.medical-wave {
    position: relative;
    overflow: hidden;
}

.medical-wave::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(14, 165, 233, 0.1), transparent);
    animation: medicalWave 3s ease-in-out infinite;
}

@keyframes medicalWave {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* تحسينات الاستجابة المتقدمة */
@media (max-width: 768px) {
    .medical-card-premium {
        border-radius: 15px;
        margin-bottom: 1rem;
    }
    
    .medical-btn-advanced {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }
    
    .medical-table-advanced {
        border-radius: 15px;
    }
}

@media (max-width: 576px) {
    .medical-card-premium {
        border-radius: 12px;
    }
    
    .medical-btn-advanced {
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
        border-radius: 20px;
    }
    
    .medical-badge-advanced {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
        border-radius: 15px;
    }
}

/* تأثيرات الظلال المتقدمة */
.medical-shadow-soft {
    box-shadow: 0 2px 20px rgba(14, 165, 233, 0.1);
}

.medical-shadow-medium {
    box-shadow: 0 4px 30px rgba(14, 165, 233, 0.15);
}

.medical-shadow-strong {
    box-shadow: 0 8px 40px rgba(14, 165, 233, 0.2);
}

.medical-shadow-glow-hover:hover {
    box-shadow: 0 0 30px rgba(14, 165, 233, 0.4);
}

/* تأثيرات الحدود المتقدمة */
.medical-border-glow {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(45deg, #0ea5e9, #06b6d4) border-box;
    border-radius: 15px;
}

.medical-border-animated {
    border: 2px solid #0ea5e9;
    border-radius: 15px;
    position: relative;
    overflow: hidden;
}

.medical-border-animated::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #0ea5e9, #06b6d4, #0891b2, #0e7490);
    background-size: 300% 300%;
    border-radius: 15px;
    z-index: -1;
    animation: medicalBorderMove 3s ease infinite;
}

@keyframes medicalBorderMove {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}
