{% extends "base.html" %}

{% block title %}ALEMIS - إجازاتي{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/medical-theme.css') }}">
<style>
    .leaves-header {
        background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 50%, #0369a1 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: 0 8px 32px rgba(14, 165, 233, 0.3);
        position: relative;
        overflow: hidden;
    }

    .leaves-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -10%;
        width: 200px;
        height: 200px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='rgba(255,255,255,0.1)'%3E%3Cpath d='M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z'/%3E%3C/svg%3E") no-repeat center;
        background-size: contain;
        animation: float 6s ease-in-out infinite;
    }

    .leaves-header::after {
        content: '';
        position: absolute;
        bottom: -30%;
        left: -5%;
        width: 150px;
        height: 150px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='rgba(255,255,255,0.1)'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E") no-repeat center;
        background-size: contain;
        animation: float 8s ease-in-out infinite reverse;
    }

    .leaves-title {
        font-size: 2.5rem;
        font-weight: 700;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        margin-bottom: 0.5rem;
    }

    .leaves-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        font-weight: 300;
    }

    .leaves-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: rgba(255,255,255,0.8);
    }

    .leave-type-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 500;
        font-size: 0.85rem;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        transition: all 0.3s ease;
    }

    .leave-type-annual {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
    }

    .leave-type-sick {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: white;
    }

    .leave-type-emergency {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        color: white;
    }

    .leave-type-unpaid {
        background: linear-gradient(135deg, #6b7280, #4b5563);
        color: white;
    }

    .leave-type-coverage {
        background: linear-gradient(135deg, #06b6d4, #0891b2);
        color: white;
    }

    .duration-badge {
        background: rgba(14, 165, 233, 0.1);
        color: var(--medical-primary-dark);
        padding: 0.4rem 0.8rem;
        border-radius: 15px;
        font-weight: 600;
        border: 2px solid rgba(14, 165, 233, 0.2);
    }

    .approval-badge {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        transition: all 0.3s ease;
    }

    .approval-badge.approved {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        animation: pulse 2s ease-in-out infinite;
    }

    .approval-badge.pending {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        color: white;
        animation: spin 2s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<!-- Leaves Header -->
<div class="leaves-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="leaves-icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <h1 class="leaves-title">إجازاتي</h1>
                <p class="leaves-subtitle">إدارة ومتابعة طلبات الإجازة الخاصة بي</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="leaves-stats">
                    <div class="stat-item">
                        <i class="fas fa-calendar-plus"></i>
                        <span>نظام إجازات متطور</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <div class="col-md-12">
            <!-- Leaves Card -->
            <div class="card medical-card medical-card-premium medical-glow medical-particles mb-4 fade-in">
                <div class="card-header medical-card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="header-icon medical-pulse">
                                <i class="fas fa-list-alt medical-icon-advanced"></i>
                            </div>
                            <div class="header-content">
                                <h5 class="mb-0">قائمة طلبات الإجازة</h5>
                                <small class="opacity-75">جميع طلبات الإجازة المقدمة</small>
                            </div>
                        </div>
                        <a href="{{ url_for('new_leave') }}" class="btn medical-btn-advanced medical-shadow-glow-hover">
                            <i class="fas fa-plus me-1 medical-icon-advanced"></i>
                            طلب إجازة جديدة
                        </a>
                    </div>
                </div>
                <div class="card-body medical-card-body">
                    {% if leave_requests %}
                    <div class="table-responsive medical-table-container medical-table-advanced">
                        <table class="table medical-table">
                            <thead class="medical-table-header">
                                <tr>
                                    <th><i class="fas fa-tag me-2"></i>نوع الإجازة</th>
                                    <th><i class="fas fa-calendar-day me-2"></i>تاريخ البداية</th>
                                    <th><i class="fas fa-calendar-day me-2"></i>تاريخ النهاية</th>
                                    <th><i class="fas fa-clock me-2"></i>المدة</th>
                                    <th><i class="fas fa-comment me-2"></i>السبب</th>
                                    <th><i class="fas fa-info-circle me-2"></i>الحالة</th>
                                    <th><i class="fas fa-user-tie me-2"></i>موافقة المدير</th>
                                    <th><i class="fas fa-users me-2"></i>موافقة الموارد البشرية</th>
                                    <th><i class="fas fa-calendar-plus me-2"></i>تاريخ الطلب</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for leave in leave_requests %}
                                <tr class="medical-table-row medical-table-row-advanced medical-wave">
                                    <td>
                                        {% if leave.leave_type_name == 'إجازة اعتيادية' %}
                                        <span class="leave-type-badge leave-type-annual">
                                            <i class="fas fa-calendar-alt me-1"></i>{{ leave.leave_type_name }}
                                        </span>
                                        {% elif leave.leave_type_name == 'إجازة مرضية' %}
                                        <span class="leave-type-badge leave-type-sick">
                                            <i class="fas fa-user-injured me-1"></i>{{ leave.leave_type_name }}
                                        </span>
                                        {% elif leave.leave_type_name == 'إجازة اضطرارية' %}
                                        <span class="leave-type-badge leave-type-emergency">
                                            <i class="fas fa-exclamation-triangle me-1"></i>{{ leave.leave_type_name }}
                                        </span>
                                        {% elif leave.leave_type_name == 'إجازة بدون راتب' %}
                                        <span class="leave-type-badge leave-type-unpaid">
                                            <i class="fas fa-ban me-1"></i>{{ leave.leave_type_name }}
                                        </span>
                                        {% elif leave.leave_type_name == 'بدل يوم تغطية' %}
                                        <span class="leave-type-badge leave-type-coverage">
                                            <i class="fas fa-exchange-alt me-1"></i>{{ leave.leave_type_name }}
                                        </span>
                                        {% else %}
                                        <span class="leave-type-badge leave-type-annual">
                                            <i class="fas fa-calendar me-1"></i>{{ leave.leave_type_name }}
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="date-info">
                                            <i class="fas fa-calendar-day text-primary me-1"></i>
                                            {{ leave.start_date }}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="date-info">
                                            <i class="fas fa-calendar-day text-primary me-1"></i>
                                            {{ leave.end_date }}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="duration-badge">
                                            {% set start_date = leave.start_date.split('-') %}
                                            {% set end_date = leave.end_date.split('-') %}
                                            {% set start = [start_date[0]|int, start_date[1]|int, start_date[2]|int] %}
                                            {% set end = [end_date[0]|int, end_date[1]|int, end_date[2]|int] %}
                                            {% set days = (end[0] - start[0]) * 365 + (end[1] - start[1]) * 30 + (end[2] - start[2]) + 1 %}
                                            <i class="fas fa-clock me-1"></i>{{ days }} يوم
                                        </span>
                                    </td>
                                    <td>
                                        <div class="reason-text">
                                            <i class="fas fa-quote-left text-muted me-1"></i>
                                            {{ leave.reason }}
                                        </div>
                                    </td>
                                    <td>
                                        {% if leave.status == 'pending' %}
                                        <span class="badge medical-status-badge" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                                            <i class="fas fa-hourglass-half me-1"></i>قيد الانتظار
                                        </span>
                                        {% elif leave.status == 'approved' %}
                                        <span class="badge medical-status-badge status-approved">
                                            <i class="fas fa-check-circle me-1"></i>تمت الموافقة
                                        </span>
                                        {% elif leave.status == 'rejected' %}
                                        <span class="badge medical-status-badge status-rejected">
                                            <i class="fas fa-times-circle me-1"></i>مرفوض
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if leave.manager_approval == 1 %}
                                        <span class="approval-badge approved">
                                            <i class="fas fa-check"></i>
                                        </span>
                                        {% else %}
                                        <span class="approval-badge pending">
                                            <i class="fas fa-clock"></i>
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if leave.hr_approval == 1 %}
                                        <span class="approval-badge approved">
                                            <i class="fas fa-check"></i>
                                        </span>
                                        {% else %}
                                        <span class="approval-badge pending">
                                            <i class="fas fa-clock"></i>
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="date-info">
                                            <i class="fas fa-calendar-plus text-muted me-1"></i>
                                            {{ leave.created_at }}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="medical-alert medical-alert-info">
                        <div class="alert-icon">
                            <i class="fas fa-calendar-times"></i>
                        </div>
                        <div class="alert-content">
                            <h6 class="mb-1">لا توجد طلبات إجازة</h6>
                            <p class="mb-0">لم تقم بتقديم أي طلبات إجازة حتى الآن. يمكنك تقديم طلب إجازة جديد من خلال الزر أعلاه.</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
