import sqlite3
import os

print('بدء إنشاء قاعدة البيانات...')

# إنشاء قاعدة البيانات
conn = sqlite3.connect('alemis.db')
cursor = conn.cursor()

print('إنشاء الجداول...')

# إنشاء الجداول الأساسية
cursor.execute('''
CREATE TABLE IF NOT EXISTS departments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT
)
''')

cursor.execute('''
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    email TEXT,
    phone TEXT,
    role TEXT NOT NULL DEFAULT 'employee',
    department_id INTEGER,
    is_active INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (department_id) REFERENCES departments (id)
)
''')

cursor.execute('''
CREATE TABLE IF NOT EXISTS leave_types (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    max_days INTEGER DEFAULT 30,
    requires_approval INTEGER DEFAULT 1
)
''')

cursor.execute('''
CREATE TABLE IF NOT EXISTS leave_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    leave_type_id INTEGER NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    reason TEXT,
    status TEXT DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIGN KEY (leave_type_id) REFERENCES leave_types (id)
)
''')

cursor.execute('''
CREATE TABLE IF NOT EXISTS coverage_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    coverage_date DATE NOT NULL,
    coverage_type TEXT NOT NULL,
    reason TEXT,
    status TEXT DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
)
''')

print('إدراج البيانات الأساسية...')

# إدراج بيانات أساسية
cursor.execute('INSERT OR IGNORE INTO departments (name, description) VALUES (?, ?)', ('المختبر', 'قسم المختبر الطبي'))

cursor.execute('INSERT OR IGNORE INTO leave_types (name) VALUES (?)', ('إجازة سنوية',))
cursor.execute('INSERT OR IGNORE INTO leave_types (name) VALUES (?)', ('إجازة مرضية',))
cursor.execute('INSERT OR IGNORE INTO leave_types (name) VALUES (?)', ('إجازة طارئة',))

print('إنشاء المستخدمين التجريبيين...')

# إنشاء مستخدمين تجريبيين
cursor.execute('''INSERT OR IGNORE INTO users
    (username, password, first_name, last_name, role, department_id)
    VALUES (?, ?, ?, ?, ?, ?)''',
    ('admin', 'admin123', 'مدير', 'النظام', 'admin', 1))

cursor.execute('''INSERT OR IGNORE INTO users
    (username, password, first_name, last_name, role, department_id)
    VALUES (?, ?, ?, ?, ?, ?)''',
    ('employee1', 'emp123', 'موظف', 'تجريبي', 'employee', 1))

cursor.execute('''INSERT OR IGNORE INTO users
    (username, password, first_name, last_name, role, department_id)
    VALUES (?, ?, ?, ?, ?, ?)''',
    ('hr1', 'hr123', 'موارد', 'بشرية', 'hr', 1))

cursor.execute('''INSERT OR IGNORE INTO users
    (username, password, first_name, last_name, role, department_id)
    VALUES (?, ?, ?, ?, ?, ?)''',
    ('manager1', 'mgr123', 'مدير', 'المختبر', 'manager', 1))

cursor.execute('''INSERT OR IGNORE INTO users
    (username, password, first_name, last_name, role, department_id)
    VALUES (?, ?, ?, ?, ?, ?)''',
    ('gm1', 'gm123', 'مدير', 'عام', 'gm', 1))

conn.commit()
conn.close()
print('تم إنشاء قاعدة البيانات بنجاح!')
print('')
print('الحسابات المتاحة:')
print('- admin / admin123 (مدير النظام)')
print('- employee1 / emp123 (موظف)')
print('- hr1 / hr123 (موارد بشرية)')
print('- manager1 / mgr123 (مدير المختبر)')
print('- gm1 / gm123 (مدير عام)')
print('')
print('يمكنك الآن تشغيل الموقع بالأمر: python app.py')
