{% extends "base.html" %}

{% block title %}ALEMIS - طلب تبديل دوام جديد{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <h2 class="mb-4">طلب تبديل دوام جديد</h2>
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-sync-alt me-1"></i>
                نموذج طلب تبديل دوام
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('new_shift_swap') }}">
                    <div class="mb-3">
                        <label for="swap_type" class="form-label">إجراء التبديل</label>
                        <select class="form-select" id="swap_type" name="swap_type" required>
                            <option value="">-- اختر إجراء التبديل --</option>
                            <option value="with_employee">تبديل مع موظف آخر</option>
                            <option value="without_employee">تبديل بدون موظف</option>
                            <option value="use_coverage_day">استغناء يوم تغطية</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="swap_reason_type" class="form-label">نوع التبديل</label>
                        <select class="form-select" id="swap_reason_type" name="swap_reason_type" required>
                            <option value="">-- اختر نوع التبديل --</option>
                            <option value="regular_shift">دوام رسمي</option>
                            <option value="coverage_day">يوم تغطية</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="swap_duration" class="form-label">مدة التبديل</label>
                        <select class="form-select" id="swap_duration" name="swap_duration" required>
                            <option value="">-- اختر مدة التبديل --</option>
                            <option value="one_day">يوم واحد</option>
                            <option value="multiple_days">أكثر من يوم</option>
                        </select>
                    </div>

                    <div class="mb-3" id="swap_with_employee_div" style="display: none;">
                        <label for="swap_with_user_id" class="form-label">التبديل مع الموظف</label>
                        <select class="form-select" id="swap_with_user_id" name="swap_with_user_id">
                            <option value="">-- اختر الموظف --</option>
                            {% for employee in employees %}
                            {% if employee.id != session.user_id %}
                            <option value="{{ employee.id }}">{{ employee.first_name }} {{ employee.last_name }}</option>
                            {% endif %}
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3" id="coverage_day_div" style="display: none;">
                        <label for="coverage_day_id" class="form-label">يوم التغطية المراد استغنائه</label>
                        <select class="form-select" id="coverage_day_id" name="coverage_day_id">
                            <option value="">-- اختر يوم التغطية --</option>
                            {% for coverage_day in coverage_days %}
                            <option value="{{ coverage_day.id }}">{{ coverage_day.coverage_date }} -
                                {% if coverage_day.shift_type == 'morning' %}
                                دوام صباحي (8ص-4م)
                                {% elif coverage_day.shift_type == 'evening' %}
                                دوام مسائي (4م-12ل)
                                {% elif coverage_day.shift_type == 'night' %}
                                دوام ليلي (12ل-8ص)
                                {% endif %}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3" id="single_day_div" style="display: none;">
                        <label for="swap_date" class="form-label">تاريخ التبديل</label>
                        <input type="date" class="form-control" id="swap_date" name="swap_date">
                    </div>

                    <div id="multiple_days_div" style="display: none;">
                        <div class="mb-3">
                            <label for="start_date" class="form-label">تاريخ البداية</label>
                            <input type="date" class="form-control" id="start_date" name="start_date">
                        </div>

                        <div class="mb-3">
                            <label for="end_date" class="form-label">تاريخ النهاية</label>
                            <input type="date" class="form-control" id="end_date" name="end_date">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="shift_type" class="form-label">نوع الشفت</label>
                        <select class="form-select" id="shift_type" name="shift_type" required>
                            <option value="">-- اختر نوع الشفت --</option>
                            <option value="morning">دوام صباحي (8ص-4م)</option>
                            <option value="evening">دوام مسائي (4م-12ل)</option>
                            <option value="night">دوام ليلي (12ل-8ص)</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="reason" class="form-label">سبب التبديل</label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" required></textarea>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-1"></i>
                            إرسال الطلب
                        </button>
                        <a href="{{ url_for('shift_swap_requests') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-1"></i>
                            العودة إلى قائمة الطلبات
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // إظهار/إخفاء حقل الموظف حسب نوع التبديل
        const swapTypeSelect = document.getElementById('swap_type');
        const swapWithEmployeeDiv = document.getElementById('swap_with_employee_div');
        const swapWithUserIdSelect = document.getElementById('swap_with_user_id');
        const coverageDayDiv = document.getElementById('coverage_day_div');
        const coverageDayIdSelect = document.getElementById('coverage_day_id');

        // إظهار/إخفاء حقول التاريخ حسب مدة التبديل
        const swapDurationSelect = document.getElementById('swap_duration');
        const singleDayDiv = document.getElementById('single_day_div');
        const multipleDaysDiv = document.getElementById('multiple_days_div');
        const swapDateInput = document.getElementById('swap_date');
        const startDateInput = document.getElementById('start_date');
        const endDateInput = document.getElementById('end_date');
        const shiftTypeSelect = document.getElementById('shift_type');

        swapTypeSelect.addEventListener('change', function() {
            if (this.value === 'with_employee') {
                swapWithEmployeeDiv.style.display = 'block';
                coverageDayDiv.style.display = 'none';
                swapWithUserIdSelect.setAttribute('required', 'required');
                coverageDayIdSelect.removeAttribute('required');
                swapDurationSelect.parentElement.style.display = 'block';
                shiftTypeSelect.setAttribute('required', 'required');
            } else if (this.value === 'use_coverage_day') {
                swapWithEmployeeDiv.style.display = 'none';
                coverageDayDiv.style.display = 'block';
                swapWithUserIdSelect.removeAttribute('required');
                coverageDayIdSelect.setAttribute('required', 'required');
                swapDurationSelect.parentElement.style.display = 'none';
                singleDayDiv.style.display = 'none';
                multipleDaysDiv.style.display = 'none';
                shiftTypeSelect.removeAttribute('required');
            } else {
                swapWithEmployeeDiv.style.display = 'none';
                coverageDayDiv.style.display = 'none';
                swapWithUserIdSelect.removeAttribute('required');
                coverageDayIdSelect.removeAttribute('required');
                swapDurationSelect.parentElement.style.display = 'block';
                shiftTypeSelect.setAttribute('required', 'required');
            }
        });

        swapDurationSelect.addEventListener('change', function() {
            if (this.value === 'one_day') {
                singleDayDiv.style.display = 'block';
                multipleDaysDiv.style.display = 'none';
                swapDateInput.setAttribute('required', 'required');
                startDateInput.removeAttribute('required');
                endDateInput.removeAttribute('required');
            } else if (this.value === 'multiple_days') {
                singleDayDiv.style.display = 'none';
                multipleDaysDiv.style.display = 'block';
                swapDateInput.removeAttribute('required');
                startDateInput.setAttribute('required', 'required');
                endDateInput.setAttribute('required', 'required');
            } else {
                singleDayDiv.style.display = 'none';
                multipleDaysDiv.style.display = 'none';
                swapDateInput.removeAttribute('required');
                startDateInput.removeAttribute('required');
                endDateInput.removeAttribute('required');
            }
        });

        // التحقق من التواريخ
        const form = document.querySelector('form');
        form.addEventListener('submit', function(e) {
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            if (swapDurationSelect.value === 'one_day') {
                const swapDate = new Date(swapDateInput.value);
                if (swapDate < today) {
                    e.preventDefault();
                    alert('لا يمكن اختيار تاريخ في الماضي');
                    return;
                }
            } else if (swapDurationSelect.value === 'multiple_days') {
                const startDate = new Date(startDateInput.value);
                const endDate = new Date(endDateInput.value);

                if (startDate < today) {
                    e.preventDefault();
                    alert('لا يمكن اختيار تاريخ بداية في الماضي');
                    return;
                }

                if (endDate < today) {
                    e.preventDefault();
                    alert('لا يمكن اختيار تاريخ نهاية في الماضي');
                    return;
                }

                if (startDate > endDate) {
                    e.preventDefault();
                    alert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
                    return;
                }
            }

            if (swapTypeSelect.value === 'with_employee' && !swapWithUserIdSelect.value) {
                e.preventDefault();
                alert('يرجى اختيار الموظف للتبديل معه');
                return;
            }

            if (swapTypeSelect.value === 'use_coverage_day' && !coverageDayIdSelect.value) {
                e.preventDefault();
                alert('يرجى اختيار يوم التغطية المراد استغنائه');
                return;
            }

            if (swapTypeSelect.value !== 'use_coverage_day' && !shiftTypeSelect.value) {
                e.preventDefault();
                alert('يرجى اختيار نوع الشفت');
                return;
            }
        });
    });
</script>
{% endblock %}
