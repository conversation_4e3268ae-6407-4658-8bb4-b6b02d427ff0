#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from datetime import datetime

def add_new_leave_types():
    """إضافة أنواع إجازات جديدة للنظام"""

    print("🏥 إضافة أنواع إجازات جديدة...")

    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('employees.db')
    cursor = conn.cursor()

    # أنواع الإجازات الجديدة
    new_leave_types = [
        {
            'name': 'إجازة أمومة',
            'description': 'إجازة الأمومة للموظفات (90 يوم)',
            'default_days': 90,
            'requires_approval': 1,
            'max_consecutive_days': 90,
            'requires_medical_certificate': 1,
            'gender_specific': 'female'
        },
        {
            'name': 'إجازة أبوة',
            'description': 'إجازة الأبوة للموظفين (15 يوم)',
            'default_days': 15,
            'requires_approval': 1,
            'max_consecutive_days': 15,
            'requires_medical_certificate': 0,
            'gender_specific': 'male'
        },
        {
            'name': 'إجازة حج وعمرة',
            'description': 'إجازة الحج والعمرة (30 يوم مرة كل 5 سنوات)',
            'default_days': 30,
            'requires_approval': 1,
            'max_consecutive_days': 30,
            'requires_medical_certificate': 0,
            'frequency_limit': '5_years'
        },
        {
            'name': 'إجازة دراسية',
            'description': 'إجازة للامتحانات والدورات التدريبية',
            'default_days': 10,
            'requires_approval': 1,
            'max_consecutive_days': 5,
            'requires_medical_certificate': 0,
            'requires_documents': 1
        },
        {
            'name': 'إجازة مرافقة مريض',
            'description': 'إجازة لمرافقة مريض من الأقارب',
            'default_days': 7,
            'requires_approval': 1,
            'max_consecutive_days': 7,
            'requires_medical_certificate': 1,
            'emergency_type': 1
        },
        {
            'name': 'إجازة زواج',
            'description': 'إجازة الزواج للموظف أو أقاربه',
            'default_days': 7,
            'requires_approval': 1,
            'max_consecutive_days': 7,
            'requires_medical_certificate': 0,
            'requires_documents': 1
        },
        {
            'name': 'إجازة وفاة',
            'description': 'إجازة الوفاة حسب درجة القرابة',
            'default_days': 3,
            'requires_approval': 1,
            'max_consecutive_days': 7,
            'requires_medical_certificate': 0,
            'emergency_type': 1,
            'variable_days': 1  # الأيام تختلف حسب درجة القرابة
        },
        {
            'name': 'إجازة استثنائية',
            'description': 'إجازة للظروف الاستثنائية والطارئة',
            'default_days': 5,
            'requires_approval': 1,
            'max_consecutive_days': 10,
            'requires_medical_certificate': 0,
            'emergency_type': 1,
            'requires_justification': 1
        },
        {
            'name': 'إجازة مرضية طويلة',
            'description': 'إجازة مرضية لأكثر من 30 يوم',
            'default_days': 60,
            'requires_approval': 1,
            'max_consecutive_days': 180,
            'requires_medical_certificate': 1,
            'requires_medical_committee': 1
        },
        {
            'name': 'إجازة بدون راتب',
            'description': 'إجازة بدون راتب للظروف الخاصة',
            'default_days': 30,
            'requires_approval': 1,
            'max_consecutive_days': 90,
            'requires_medical_certificate': 0,
            'unpaid': 1,
            'requires_justification': 1
        }
    ]

    # التحقق من وجود الأعمدة الجديدة وإضافتها إذا لم تكن موجودة
    try:
        cursor.execute("PRAGMA table_info(leave_types)")
        columns = [column[1] for column in cursor.fetchall()]

        new_columns = [
            ('requires_approval', 'INTEGER DEFAULT 1'),
            ('max_consecutive_days', 'INTEGER DEFAULT 30'),
            ('requires_medical_certificate', 'INTEGER DEFAULT 0'),
            ('gender_specific', 'TEXT'),
            ('frequency_limit', 'TEXT'),
            ('requires_documents', 'INTEGER DEFAULT 0'),
            ('emergency_type', 'INTEGER DEFAULT 0'),
            ('variable_days', 'INTEGER DEFAULT 0'),
            ('requires_justification', 'INTEGER DEFAULT 0'),
            ('requires_medical_committee', 'INTEGER DEFAULT 0'),
            ('unpaid', 'INTEGER DEFAULT 0'),
            ('is_active', 'INTEGER DEFAULT 1'),
            ('created_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP'),
            ('updated_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP')
        ]

        for column_name, column_def in new_columns:
            if column_name not in columns:
                try:
                    cursor.execute(f'ALTER TABLE leave_types ADD COLUMN {column_name} {column_def}')
                    print(f"✅ تم إضافة عمود {column_name}")
                except sqlite3.Error as e:
                    print(f"⚠️ خطأ في إضافة عمود {column_name}: {e}")
    except sqlite3.Error as e:
        print(f"⚠️ خطأ في الوصول إلى جدول leave_types: {e}")
        return

    # إضافة أنواع الإجازات الجديدة
    for leave_type in new_leave_types:
        # التحقق من عدم وجود نوع الإجازة مسبقاً
        cursor.execute('SELECT id FROM leave_types WHERE name = ?', (leave_type['name'],))
        existing = cursor.fetchone()

        if not existing:
            # إعداد القيم للإدراج
            columns_to_insert = ['name', 'description', 'default_days', 'requires_approval', 'max_consecutive_days']
            values_to_insert = [
                leave_type['name'],
                leave_type['description'],
                leave_type['default_days'],
                leave_type['requires_approval'],
                leave_type['max_consecutive_days']
            ]

            # إضافة الأعمدة الاختيارية
            optional_columns = [
                'requires_medical_certificate', 'gender_specific', 'frequency_limit',
                'requires_documents', 'emergency_type', 'variable_days',
                'requires_justification', 'requires_medical_committee', 'unpaid'
            ]

            for col in optional_columns:
                if col in leave_type:
                    columns_to_insert.append(col)
                    values_to_insert.append(leave_type[col])

            # إضافة تاريخ الإنشاء
            columns_to_insert.extend(['created_at', 'updated_at'])
            values_to_insert.extend([datetime.now(), datetime.now()])

            # تنفيذ الإدراج
            placeholders = ', '.join(['?' for _ in values_to_insert])
            columns_str = ', '.join(columns_to_insert)

            cursor.execute(
                f'INSERT INTO leave_types ({columns_str}) VALUES ({placeholders})',
                values_to_insert
            )

            print(f"✅ تم إضافة نوع إجازة: {leave_type['name']}")
        else:
            print(f"ℹ️ نوع الإجازة موجود بالفعل: {leave_type['name']}")

    # إنشاء جدول قواعد الإجازات إذا لم يكن موجوداً
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS leave_rules (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            leave_type_id INTEGER,
            rule_type TEXT NOT NULL,
            rule_value TEXT NOT NULL,
            description TEXT,
            is_active INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (leave_type_id) REFERENCES leave_types (id)
        )
    ''')

    # إضافة قواعد خاصة لبعض أنواع الإجازات
    leave_rules = [
        {
            'leave_type_name': 'إجازة وفاة',
            'rules': [
                {'rule_type': 'relationship_days', 'rule_value': 'parent:7,spouse:7,child:7,sibling:3,grandparent:3', 'description': 'أيام الإجازة حسب درجة القرابة'},
                {'rule_type': 'max_per_year', 'rule_value': '2', 'description': 'حد أقصى مرتين في السنة'}
            ]
        },
        {
            'leave_type_name': 'إجازة حج وعمرة',
            'rules': [
                {'rule_type': 'frequency', 'rule_value': '5_years', 'description': 'مرة كل 5 سنوات'},
                {'rule_type': 'advance_notice', 'rule_value': '60', 'description': 'إشعار مسبق 60 يوم'}
            ]
        },
        {
            'leave_type_name': 'إجازة أمومة',
            'rules': [
                {'rule_type': 'gender', 'rule_value': 'female', 'description': 'للموظفات فقط'},
                {'rule_type': 'medical_required', 'rule_value': 'birth_certificate', 'description': 'شهادة ميلاد مطلوبة'}
            ]
        }
    ]

    for leave_rule_set in leave_rules:
        # الحصول على معرف نوع الإجازة
        cursor.execute('SELECT id FROM leave_types WHERE name = ?', (leave_rule_set['leave_type_name'],))
        leave_type_result = cursor.fetchone()

        if leave_type_result:
            leave_type_id = leave_type_result[0]

            for rule in leave_rule_set['rules']:
                # التحقق من عدم وجود القاعدة مسبقاً
                cursor.execute(
                    'SELECT id FROM leave_rules WHERE leave_type_id = ? AND rule_type = ?',
                    (leave_type_id, rule['rule_type'])
                )
                existing_rule = cursor.fetchone()

                if not existing_rule:
                    cursor.execute('''
                        INSERT INTO leave_rules (leave_type_id, rule_type, rule_value, description, created_at)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (leave_type_id, rule['rule_type'], rule['rule_value'], rule['description'], datetime.now()))

                    print(f"✅ تم إضافة قاعدة: {rule['description']} لنوع الإجازة: {leave_rule_set['leave_type_name']}")

    # إنشاء أرصدة الإجازات للمستخدمين الموجودين
    print("🔄 تحديث أرصدة الإجازات للمستخدمين...")

    # الحصول على جميع المستخدمين
    cursor.execute('SELECT id FROM users WHERE role = "employee" AND is_active = 1')
    users = cursor.fetchall()

    # الحصول على أنواع الإجازات الجديدة
    cursor.execute('SELECT id, name, default_days FROM leave_types WHERE name IN ({})'.format(
        ','.join(['?' for _ in new_leave_types])
    ), [lt['name'] for lt in new_leave_types])
    new_leave_type_records = cursor.fetchall()

    current_year = datetime.now().year

    for user_id, in users:
        for leave_type_id, leave_type_name, default_days in new_leave_type_records:
            # التحقق من عدم وجود رصيد مسبق
            cursor.execute('''
                SELECT id FROM leave_balances
                WHERE user_id = ? AND leave_type_id = ? AND year = ?
            ''', (user_id, leave_type_id, current_year))

            existing_balance = cursor.fetchone()

            if not existing_balance:
                # إنشاء رصيد جديد
                cursor.execute('''
                    INSERT INTO leave_balances
                    (user_id, leave_type_id, year, total_days, used_days, remaining_days)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (user_id, leave_type_id, current_year, default_days, 0, default_days))

    print(f"✅ تم تحديث أرصدة الإجازات لـ {len(users)} موظف")

    # حفظ التغييرات
    conn.commit()
    conn.close()

    print("✅ تم إضافة جميع أنواع الإجازات الجديدة بنجاح!")
    print("\n📋 أنواع الإجازات المضافة:")
    for leave_type in new_leave_types:
        print(f"   • {leave_type['name']} ({leave_type['default_days']} يوم)")

if __name__ == '__main__':
    add_new_leave_types()
