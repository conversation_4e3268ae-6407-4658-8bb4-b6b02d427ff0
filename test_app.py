#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("🔍 فحص المكتبات المطلوبة...")
    
    # Test Flask import
    try:
        from flask import Flask
        print("✅ Flask - تم تحميلها بنجاح")
    except ImportError as e:
        print(f"❌ Flask - خطأ في التحميل: {e}")
        sys.exit(1)
    
    # Test SQLite
    try:
        import sqlite3
        print("✅ SQLite3 - تم تحميلها بنجاح")
    except ImportError as e:
        print(f"❌ SQLite3 - خطأ في التحميل: {e}")
        sys.exit(1)
    
    # Test other imports
    try:
        from datetime import datetime, timedelta, date
        import secrets
        import hashlib
        import calendar
        from werkzeug.utils import secure_filename
        print("✅ المكتبات الأساسية - تم تحميلها بنجاح")
    except ImportError as e:
        print(f"❌ المكتبات الأساسية - خطأ في التحميل: {e}")
        sys.exit(1)
    
    print("\n🚀 بدء تشغيل التطبيق...")
    
    # Import main app
    from app import app
    print("✅ تم تحميل التطبيق بنجاح")
    
    print("\n📍 الخادم متاح على: http://localhost:5000")
    print("🔑 حسابات الاختبار:")
    print("   - المدير: admin / admin123")
    print("   - مدير المختبر: manager / admin123")
    print("   - الموارد البشرية: hr / admin123")
    print("   - المدير العام: gm / admin123")
    print("   - موظف 1: employee1 / admin123")
    print("   - موظف 2: employee2 / admin123")
    print("=" * 50)
    
    # Run the app
    app.run(debug=True, host='0.0.0.0', port=5000)
    
except Exception as e:
    print(f"❌ خطأ في تشغيل التطبيق: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
