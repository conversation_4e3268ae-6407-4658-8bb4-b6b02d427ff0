{% extends "base.html" %}

{% block title %}ALEMIS - طلبات الإجازات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">طلبات الإجازات</h2>
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-clipboard-list me-1"></i>
                قائمة طلبات الإجازة
            </div>
            <div class="card-body">
                {% if leave_requests %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>الموظف</th>
                                {% if user.role in ['admin', 'hr', 'gm'] %}
                                <th>القسم</th>
                                {% endif %}
                                <th>نوع الإجازة</th>
                                <th>تاريخ البداية</th>
                                <th>تاريخ النهاية</th>
                                <th>المدة</th>
                                <th>السبب</th>
                                <th>الحالة</th>
                                <th>موافقة المدير</th>
                                <th>موافقة الموارد البشرية</th>
                                <th>موافقة مدير المستشفى</th>
                                <th>تاريخ الطلب</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for leave in leave_requests %}
                            <tr>
                                <td>{{ leave.first_name }} {{ leave.last_name }}</td>
                                {% if user.role in ['admin', 'hr', 'gm'] %}
                                <td>{{ leave.department_name }}</td>
                                {% endif %}
                                <td>{{ leave.leave_type_name }}</td>
                                <td>{{ leave.start_date }}</td>
                                <td>{{ leave.end_date }}</td>
                                <td>
                                    {% set start_date = leave.start_date.split('-') %}
                                    {% set end_date = leave.end_date.split('-') %}
                                    {% set start = [start_date[0]|int, start_date[1]|int, start_date[2]|int] %}
                                    {% set end = [end_date[0]|int, end_date[1]|int, end_date[2]|int] %}
                                    {% set days = (end[0] - start[0]) * 365 + (end[1] - start[1]) * 30 + (end[2] - start[2]) + 1 %}
                                    {{ days }} يوم
                                </td>
                                <td>{{ leave.reason }}</td>
                                <td>
                                    {% if leave.status == 'pending' %}
                                    <span class="badge bg-warning">قيد الانتظار</span>
                                    {% elif leave.status == 'approved' %}
                                    <span class="badge bg-success">تمت الموافقة</span>
                                    {% elif leave.status == 'rejected' %}
                                    <span class="badge bg-danger">مرفوض</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if leave.manager_approval == 1 %}
                                    <span class="badge bg-success"><i class="fas fa-check"></i></span>
                                    {% else %}
                                    <span class="badge bg-secondary"><i class="fas fa-clock"></i></span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if leave.hr_approval == 1 %}
                                    <span class="badge bg-success"><i class="fas fa-check"></i></span>
                                    {% else %}
                                    <span class="badge bg-secondary"><i class="fas fa-clock"></i></span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if leave.hospital_manager_approval == 1 %}
                                    <span class="badge bg-success"><i class="fas fa-check"></i></span>
                                    {% else %}
                                    <span class="badge bg-secondary"><i class="fas fa-clock"></i></span>
                                    {% endif %}
                                </td>
                                <td>{{ leave.created_at }}</td>
                                <td>
                                    {% if leave.status == 'pending' %}
                                        {% if user.role == 'manager' and leave.manager_approval == 0 %}
                                        <a href="{{ url_for('approve_leave', leave_id=leave.id, role='manager') }}" class="btn btn-success btn-sm mb-1">
                                            <i class="fas fa-check me-1"></i>
                                            موافقة المدير
                                        </a>
                                        <a href="{{ url_for('reject_leave', leave_id=leave.id, role='manager') }}" class="btn btn-danger btn-sm mb-1">
                                            <i class="fas fa-times me-1"></i>
                                            رفض
                                        </a>
                                        {% endif %}

                                        {% if user.role == 'hr' and leave.manager_approval == 1 and leave.hr_approval == 0 %}
                                        <a href="{{ url_for('approve_leave', leave_id=leave.id, role='hr') }}" class="btn btn-success btn-sm mb-1">
                                            <i class="fas fa-check me-1"></i>
                                            موافقة الموارد البشرية
                                        </a>
                                        <a href="{{ url_for('reject_leave', leave_id=leave.id, role='hr') }}" class="btn btn-danger btn-sm mb-1">
                                            <i class="fas fa-times me-1"></i>
                                            رفض
                                        </a>
                                        {% endif %}

                                        {% if user.role in ['hospital_manager', 'admin'] and leave.manager_approval == 1 and leave.hr_approval == 1 and leave.hospital_manager_approval == 0 %}
                                        <a href="{{ url_for('approve_leave', leave_id=leave.id, role='hospital_manager') }}" class="btn btn-success btn-sm mb-1">
                                            <i class="fas fa-check me-1"></i>
                                            موافقة مدير المستشفى
                                        </a>
                                        <a href="{{ url_for('reject_leave', leave_id=leave.id, role='hospital_manager') }}" class="btn btn-danger btn-sm mb-1">
                                            <i class="fas fa-times me-1"></i>
                                            رفض
                                        </a>
                                        {% endif %}
                                    {% endif %}

                                    <!-- عرض التعليقات إن وجدت -->
                                    {% if leave.manager_comment or leave.hr_comment or leave.hospital_manager_comment %}
                                    <button class="btn btn-info btn-sm" data-bs-toggle="modal" data-bs-target="#commentsModal{{ leave.id }}">
                                        <i class="fas fa-comment me-1"></i>
                                        التعليقات
                                    </button>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-1"></i>
                    لا توجد طلبات إجازة حتى الآن.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- نوافذ منبثقة لعرض التعليقات -->
{% for leave in leave_requests %}
{% if leave.manager_comment or leave.hr_comment or leave.hospital_manager_comment %}
<div class="modal fade" id="commentsModal{{ leave.id }}" tabindex="-1" aria-labelledby="commentsModalLabel{{ leave.id }}" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="commentsModalLabel{{ leave.id }}">تعليقات على طلب الإجازة #{{ leave.id }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                {% if leave.manager_comment %}
                <div class="mb-3">
                    <h6 class="text-primary"><i class="fas fa-user-tie me-1"></i> تعليق مدير المختبر:</h6>
                    <p class="border-start border-primary ps-3">{{ leave.manager_comment }}</p>
                </div>
                {% endif %}

                {% if leave.hr_comment %}
                <div class="mb-3">
                    <h6 class="text-info"><i class="fas fa-users me-1"></i> تعليق الموارد البشرية:</h6>
                    <p class="border-start border-info ps-3">{{ leave.hr_comment }}</p>
                </div>
                {% endif %}

                {% if leave.hospital_manager_comment %}
                <div class="mb-3">
                    <h6 class="text-success"><i class="fas fa-hospital me-1"></i> تعليق مدير المستشفى:</h6>
                    <p class="border-start border-success ps-3">{{ leave.hospital_manager_comment }}</p>
                </div>
                {% endif %}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endfor %}

{% endblock %}
