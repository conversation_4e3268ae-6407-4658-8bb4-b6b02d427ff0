#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from datetime import datetime

def add_coverage_leave_type():
    """إضافة نوع إجازة بدل يوم تغطية"""

    # Connect to database
    conn = sqlite3.connect('alemis.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    try:
        # Check if 'بدل يوم تغطية' leave type already exists
        cursor.execute('SELECT * FROM leave_types WHERE name = ?', ('بدل يوم تغطية',))
        existing = cursor.fetchone()

        if not existing:
            # Add new leave type (check table structure first)
            cursor.execute("PRAGMA table_info(leave_types)")
            columns = [col[1] for col in cursor.fetchall()]
            print(f"أعمدة جدول leave_types: {columns}")

            if 'requires_approval' in columns and 'max_consecutive_days' in columns:
                cursor.execute('''
                    INSERT INTO leave_types (name, description, default_days, requires_approval, max_consecutive_days)
                    VALUES (?, ?, ?, ?, ?)
                ''', ('بدل يوم تغطية', 'إجازة بدل أيام التغطية والعمل في الإجازات الرسمية', 0, 1, 30))
            else:
                # Use basic structure
                cursor.execute('''
                    INSERT INTO leave_types (name, description, default_days)
                    VALUES (?, ?, ?)
                ''', ('بدل يوم تغطية', 'إجازة بدل أيام التغطية والعمل في الإجازات الرسمية', 0))

            print('✅ تم إضافة نوع إجازة بدل يوم تغطية')

            # Get the new leave type ID
            leave_type_id = cursor.lastrowid

            # Create balances for all existing users
            cursor.execute('SELECT id FROM users')
            users = cursor.fetchall()

            current_year = datetime.now().year

            for user in users:
                user_id = user['id']
                # Check if balance already exists
                cursor.execute('''
                    SELECT * FROM leave_balances
                    WHERE user_id = ? AND leave_type_id = ? AND year = ?
                ''', (user_id, leave_type_id, current_year))

                if not cursor.fetchone():
                    cursor.execute('''
                        INSERT INTO leave_balances (user_id, leave_type_id, year, total_days, used_days, remaining_days)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (user_id, leave_type_id, current_year, 0, 0, 0))

            print(f'✅ تم إنشاء أرصدة بدل يوم تغطية لـ {len(users)} موظف')

            conn.commit()
        else:
            print('⚠️ نوع إجازة بدل يوم تغطية موجود بالفعل')

        # Show current leave types
        cursor.execute('SELECT * FROM leave_types ORDER BY id')
        leave_types = cursor.fetchall()

        print('\n📋 أنواع الإجازات الحالية:')
        for lt in leave_types:
            print(f"  - {lt['name']} (الأيام الافتراضية: {lt['default_days']})")

    except Exception as e:
        print(f'❌ خطأ: {e}')
        conn.rollback()
    finally:
        conn.close()

if __name__ == '__main__':
    add_coverage_leave_type()
