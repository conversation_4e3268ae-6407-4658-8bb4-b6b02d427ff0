/* متغيرات الألوان */
:root {
    --primary-color: #1a4cb8;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --body-bg: #f8f9fa;
    --body-color: #212529;
    --card-bg: #ffffff;
    --card-border: #dee2e6;
    --input-bg: #ffffff;
    --input-border: #ced4da;
    --input-focus-border: #80bdff;
    --input-focus-shadow: rgba(0, 123, 255, 0.25);
    --table-border: #dee2e6;
    --table-striped-bg: rgba(0, 0, 0, 0.05);
    --table-hover-bg: rgba(0, 0, 0, 0.075);
    --modal-bg: #ffffff;
    --dropdown-bg: #ffffff;
    --dropdown-link-hover-bg: #f8f9fa;
    --navbar-bg: #ffffff;
    --navbar-color: #212529;
    --navbar-hover-color: #1a4cb8;
    --sidebar-bg: #ffffff;
    --sidebar-color: #212529;
    --sidebar-hover-bg: #f8f9fa;
    --sidebar-active-bg: #1a4cb8;
    --sidebar-active-color: #ffffff;
    --footer-bg: #f8f9fa;
    --footer-color: #6c757d;
    --shadow-color: rgba(0, 0, 0, 0.125);
}

/* الوضع الداكن */
[data-theme="dark"] {
    --primary-color: #3a6fd8;
    --secondary-color: #adb5bd;
    --success-color: #48c774;
    --info-color: #4dc0de;
    --warning-color: #ffdd57;
    --danger-color: #f14668;
    --light-color: #495057;
    --dark-color: #e9ecef;
    --body-bg: #121212;
    --body-color: #e9ecef;
    --card-bg: #1e1e1e;
    --card-border: #2d2d2d;
    --input-bg: #2d2d2d;
    --input-border: #444444;
    --input-focus-border: #3a6fd8;
    --input-focus-shadow: rgba(58, 111, 216, 0.25);
    --table-border: #2d2d2d;
    --table-striped-bg: rgba(255, 255, 255, 0.05);
    --table-hover-bg: rgba(255, 255, 255, 0.075);
    --modal-bg: #1e1e1e;
    --dropdown-bg: #1e1e1e;
    --dropdown-link-hover-bg: #2d2d2d;
    --navbar-bg: #1e1e1e;
    --navbar-color: #e9ecef;
    --navbar-hover-color: #3a6fd8;
    --sidebar-bg: #1e1e1e;
    --sidebar-color: #e9ecef;
    --sidebar-hover-bg: #2d2d2d;
    --sidebar-active-bg: #3a6fd8;
    --sidebar-active-color: #ffffff;
    --footer-bg: #1e1e1e;
    --footer-color: #adb5bd;
    --shadow-color: rgba(0, 0, 0, 0.5);
}

/* تطبيق المتغيرات */
body {
    font-family: 'Tajawal', sans-serif;
    background-color: var(--body-bg);
    color: var(--body-color);
    direction: rtl;
    text-align: right;
}

/* الروابط */
a {
    color: var(--primary-color);
    text-decoration: none;
}

a:hover {
    color: var(--primary-color);
    text-decoration: underline;
}

/* البطاقات */
.card {
    background-color: var(--card-bg);
    border-color: var(--card-border);
    box-shadow: 0 0.125rem 0.25rem var(--shadow-color);
}

.card-header {
    background-color: var(--primary-color);
    color: white;
    font-weight: 500;
}

/* الأزرار */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #1642a3;
    border-color: #1642a3;
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-info {
    background-color: var(--info-color);
    border-color: var(--info-color);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

/* النماذج */
.form-control, .form-select {
    background-color: var(--input-bg);
    border-color: var(--input-border);
    color: var(--body-color);
}

.form-control:focus, .form-select:focus {
    background-color: var(--input-bg);
    border-color: var(--input-focus-border);
    box-shadow: 0 0 0 0.25rem var(--input-focus-shadow);
    color: var(--body-color);
}

/* الجداول */
.table {
    color: var(--body-color);
    border-color: var(--table-border);
}

.table-striped > tbody > tr:nth-of-type(odd) {
    background-color: var(--table-striped-bg);
}

.table-hover > tbody > tr:hover {
    background-color: var(--table-hover-bg);
}

/* شريط التنقل */
.navbar {
    background-color: var(--navbar-bg);
    box-shadow: 0 0.125rem 0.25rem var(--shadow-color);
}

.navbar-light .navbar-nav .nav-link {
    color: var(--navbar-color);
}

.navbar-light .navbar-nav .nav-link:hover {
    color: var(--navbar-hover-color);
}

.navbar-light .navbar-nav .nav-link.active {
    color: var(--primary-color);
    font-weight: 500;
}

/* القائمة المنسدلة */
.dropdown-menu {
    background-color: var(--dropdown-bg);
    border-color: var(--card-border);
}

.dropdown-item {
    color: var(--body-color);
}

.dropdown-item:hover {
    background-color: var(--dropdown-link-hover-bg);
    color: var(--body-color);
}

/* الشارات */
.badge {
    font-weight: 500;
}

/* التنبيهات */
.alert {
    border: none;
    border-radius: 0.25rem;
}

/* مفتاح التبديل للوضع الداكن */
.theme-switch-wrapper {
    display: flex;
    align-items: center;
    margin-right: 1rem;
}

.theme-switch {
    display: inline-block;
    height: 24px;
    position: relative;
    width: 48px;
}

.theme-switch input {
    display: none;
}

.slider {
    background-color: #ccc;
    bottom: 0;
    cursor: pointer;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    transition: .4s;
    border-radius: 34px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 5px;
}

.slider:before {
    background-color: white;
    bottom: 4px;
    content: "";
    height: 16px;
    left: 4px;
    position: absolute;
    transition: .4s;
    width: 16px;
    border-radius: 50%;
    z-index: 2;
}

.sun-icon, .moon-icon {
    font-size: 12px;
    z-index: 1;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
}

.sun-icon {
    color: #f39c12;
    right: 6px;
}

.moon-icon {
    color: #f1c40f;
    left: 6px;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(24px);
}

/* تنسيقات إضافية للوضع الداكن */
[data-theme="dark"] .card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .form-control::placeholder {
    color: #adb5bd;
}

[data-theme="dark"] .modal-content {
    background-color: var(--modal-bg);
    color: var(--body-color);
}

/* تنسيقات للنماذج عند التركيز */
.form-group.focused label, .mb-3.focused label {
    color: var(--primary-color);
    font-weight: 500;
    transition: all 0.3s;
}

/* تنسيقات للصفحات */
.page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.page-link {
    color: var(--primary-color);
}

.page-link:hover {
    color: var(--primary-color);
    background-color: var(--dropdown-link-hover-bg);
}

/* تنسيقات للتذييل */
footer {
    background-color: var(--footer-bg);
    color: var(--footer-color);
    padding: 1rem 0;
    margin-top: 2rem;
}

/* تنسيقات للأيقونات */
.icon-circle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background-color: var(--light-color);
    color: var(--primary-color);
}

/* تنسيقات للرسوم البيانية */
canvas {
    max-width: 100%;
}

/* تنسيقات للطباعة */
@media print {
    body {
        background-color: white !important;
        color: black !important;
    }

    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }

    .no-print {
        display: none !important;
    }
}
