#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def create_notifications_table():
    """إنشاء جدول الإشعارات"""
    try:
        conn = sqlite3.connect('alemis.db')
        cursor = conn.cursor()
        
        # إنشاء جدول الإشعارات
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS notifications (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            title TEXT NOT NULL,
            message TEXT NOT NULL,
            type TEXT DEFAULT 'info',
            is_read INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        ''')
        
        conn.commit()
        conn.close()
        print('✅ تم إنشاء جدول notifications بنجاح')
        return True
        
    except Exception as e:
        print(f'❌ خطأ في إنشاء جدول notifications: {e}')
        return False

if __name__ == '__main__':
    create_notifications_table()
