<!DOCTYPE html>
<html lang="ar" dir="rtl" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}ALEMEIS - نظام إدارة إجازات الموظفين{% endblock %}</title>
    <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon">
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer">
    <!-- Fallback Font Awesome -->
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v6.5.1/css/all.css">

    <!-- Dark/Light Mode CSS -->
    <style>
        /* Font Awesome Fix */
        .fas, .far, .fab, .fal, .fad, .fa {
            font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "Font Awesome 5 Free", "Font Awesome 5 Pro", "FontAwesome" !important;
            font-weight: 900 !important;
            font-style: normal !important;
            font-variant: normal !important;
            text-transform: none !important;
            line-height: 1 !important;
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
        }

        .far {
            font-weight: 400 !important;
        }

        .fal {
            font-weight: 300 !important;
        }

        /* Ensure icons are visible */
        .navbar .fas, .navbar .far, .navbar .fab,
        .dropdown-menu .fas, .dropdown-menu .far, .dropdown-menu .fab,
        .nav-link .fas, .nav-link .far, .nav-link .fab,
        .dropdown-item .fas, .dropdown-item .far, .dropdown-item .fab {
            display: inline-block !important;
            visibility: visible !important;
            opacity: 1 !important;
            width: auto !important;
            height: auto !important;
            font-size: inherit !important;
        }

        /* Fix navbar visibility */
        .navbar-nav {
            display: flex !important;
        }

        .nav-item {
            display: block !important;
        }

        .nav-link {
            display: flex !important;
            align-items: center !important;
            color: rgba(255, 255, 255, 0.9) !important;
        }

        .nav-link:hover {
            color: #ffffff !important;
        }

        /* Dropdown fixes */
        .dropdown {
            position: relative !important;
        }

        .dropdown-menu {
            display: none !important;
            position: absolute !important;
            top: 100% !important;
            left: 0 !important;
            z-index: 1050 !important;
            min-width: 12rem !important;
            max-width: 20rem !important;
            padding: 0.75rem 0 !important;
            margin: 0.125rem 0 0 !important;
            font-size: 0.95rem !important;
            color: #212529 !important;
            text-align: right !important;
            list-style: none !important;
            background-color: #ffffff !important;
            background-clip: padding-box !important;
            border: 1px solid rgba(0,0,0,.15) !important;
            border-radius: 0.5rem !important;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175) !important;
        }

        .dropdown-menu.show {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        /* Dropdown positioning for RTL */
        .dropdown-menu-end {
            right: 0 !important;
            left: auto !important;
        }

        .dropdown-item {
            display: flex !important;
            align-items: center !important;
            width: 100% !important;
            padding: 0.75rem 1.5rem !important;
            clear: both !important;
            font-weight: 500 !important;
            color: #374151 !important;
            text-align: right !important;
            text-decoration: none !important;
            white-space: nowrap !important;
            background-color: transparent !important;
            border: 0 !important;
            transition: all 0.2s ease !important;
            border-radius: 0.25rem !important;
            margin: 0.125rem 0.5rem !important;
        }

        .dropdown-item:hover,
        .dropdown-item:focus {
            color: #0ea5e9 !important;
            background-color: rgba(14, 165, 233, 0.1) !important;
            transform: translateX(-3px) !important;
        }

        .dropdown-item i {
            width: 20px !important;
            margin-left: 0.75rem !important;
            text-align: center !important;
            font-size: 0.9rem !important;
        }

        .dropdown-divider {
            height: 0 !important;
            margin: 0.5rem 0 !important;
            overflow: hidden !important;
            border-top: 1px solid #e9ecef !important;
        }

        .dropdown-header {
            display: block !important;
            padding: 0.5rem 1.5rem !important;
            margin-bottom: 0 !important;
            font-size: 0.875rem !important;
            color: #6b7280 !important;
            white-space: nowrap !important;
            font-weight: 600 !important;
        }

        .dropdown-toggle::after {
            display: inline-block !important;
            margin-right: 0.255em !important;
            vertical-align: 0.255em !important;
            content: "" !important;
            border-top: 0.3em solid !important;
            border-left: 0.3em solid transparent !important;
            border-bottom: 0 !important;
            border-right: 0.3em solid transparent !important;
        }

        /* Theme switch visibility */
        .theme-switch-wrapper {
            display: flex !important;
            align-items: center !important;
        }

        /* Dark Mode Variables */
        [data-theme="dark"] {
            --bs-body-bg: #1a1a1a;
            --bs-body-color: #ffffff;
            --bs-card-bg: #2d2d2d;
            --bs-border-color: #404040;
            --bs-navbar-bg: #212529;
            --bs-sidebar-bg: #1e1e1e;
        }

        /* Light Mode Variables */
        [data-theme="light"] {
            --bs-body-bg: #ffffff;
            --bs-body-color: #212529;
            --bs-card-bg: #ffffff;
            --bs-border-color: #dee2e6;
            --bs-navbar-bg: #f8f9fa;
            --bs-sidebar-bg: #f8f9fa;
        }

        /* Apply theme variables */
        body {
            background-color: var(--bs-body-bg) !important;
            color: var(--bs-body-color) !important;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .card {
            background-color: var(--bs-card-bg) !important;
            border-color: var(--bs-border-color) !important;
        }

        .navbar {
            background-color: var(--bs-navbar-bg) !important;
            border-bottom: 1px solid var(--bs-border-color);
        }

        /* Theme Switch */
        .theme-switch-wrapper {
            display: flex;
            align-items: center;
        }

        .theme-switch {
            display: inline-block;
            height: 34px;
            position: relative;
            width: 60px;
        }

        .theme-switch input {
            display: none;
        }

        .slider {
            background-color: #ccc;
            bottom: 0;
            cursor: pointer;
            left: 0;
            position: absolute;
            right: 0;
            top: 0;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            background-color: #fff;
            bottom: 4px;
            content: "";
            height: 26px;
            left: 4px;
            position: absolute;
            transition: .4s;
            width: 26px;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #2196F3;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .sun-icon, .moon-icon {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            font-size: 14px;
            z-index: 1;
        }

        .sun-icon {
            left: 8px;
            color: #f39c12;
        }

        .moon-icon {
            right: 8px;
            color: #f1c40f;
        }

        /* Medical Theme */
        .medical-navbar {
            background: linear-gradient(135deg, #0ea5e9 0%, #0369a1 100%) !important;
            box-shadow: 0 2px 10px rgba(14, 165, 233, 0.3);
            min-height: 70px;
            padding: 0.5rem 0;
            position: relative !important;
            z-index: 1030 !important;
        }

        /* Ensure navbar dropdowns appear above everything */
        .navbar .dropdown {
            position: static !important;
        }

        .navbar .dropdown-menu {
            position: absolute !important;
            z-index: 1050 !important;
        }

        /* Fix for container overflow */
        .navbar .container {
            position: relative !important;
            overflow: visible !important;
        }

        /* Ensure dropdown menus are not clipped */
        .navbar-collapse {
            overflow: visible !important;
        }

        .navbar-nav {
            overflow: visible !important;
        }

        .nav-item {
            overflow: visible !important;
        }

        /* Navbar improvements */
        .navbar-nav .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500;
            padding: 0.75rem 1rem !important;
            border-radius: 8px;
            margin: 0 0.25rem;
            transition: all 0.3s ease;
            display: flex !important;
            align-items: center !important;
        }

        .navbar-nav .nav-link:hover {
            color: #ffffff !important;
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-1px);
        }

        .navbar-nav .nav-link.active {
            color: #ffffff !important;
            background: rgba(255, 255, 255, 0.15);
        }

        /* Dropdown improvements */
        .dropdown-menu {
            border: none;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-radius: 12px;
            padding: 0.5rem 0;
            margin-top: 0.5rem;
        }

        .dropdown-item {
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex !important;
            align-items: center !important;
        }

        .dropdown-item:hover {
            background: rgba(14, 165, 233, 0.1);
            color: #0ea5e9;
            transform: translateX(5px);
        }

        .dropdown-item i {
            width: 20px;
            margin-left: 0.75rem;
        }

        .medical-brand {
            font-weight: 700;
            color: white !important;
        }

        .brand-container {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .medical-logo {
            width: 45px;
            height: 45px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .brand-text {
            display: flex;
            flex-direction: column;
            line-height: 1.2;
        }

        .brand-name {
            font-size: 1.4rem;
            font-weight: 700;
            color: white;
        }

        .brand-subtitle {
            font-size: 0.85rem;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
        }

        /* Footer */
        .medical-footer {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: white;
            margin-top: auto;
        }

        .footer-content {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
        }

        .footer-bottom {
            background: rgba(0, 0, 0, 0.2);
            padding: 1rem 0;
        }

        .footer-brand h5 {
            color: #0ea5e9;
            font-weight: 700;
        }

        .footer-description {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 1.5rem;
        }

        .footer-links {
            list-style: none;
            padding: 0;
        }

        .footer-links li {
            margin-bottom: 0.5rem;
        }

        .footer-links a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: #0ea5e9;
        }

        .footer-contact {
            list-style: none;
            padding: 0;
        }

        .footer-contact li {
            margin-bottom: 0.8rem;
            display: flex;
            align-items: center;
        }

        .footer-section-title {
            color: #0ea5e9;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        /* Notification dropdown */
        .notification-dropdown {
            min-width: 300px;
        }

        .notification-item {
            border-bottom: 1px solid #eee;
        }

        .notification-item:last-child {
            border-bottom: none;
        }

        .notification-text {
            font-size: 0.9rem;
            margin-right: 8px;
        }

        /* Medical features */
        .medical-features {
            margin-top: 1rem;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.9);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .brand-text {
                display: none;
            }

            .medical-logo {
                width: 40px;
                height: 40px;
            }
        }

        /* إصلاحات إضافية لضمان ظهور البيانات */
        .navbar-nav .nav-link,
        .dropdown-item {
            display: flex !important;
            align-items: center !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .navbar-nav .nav-link i,
        .dropdown-item i {
            display: inline-block !important;
            visibility: visible !important;
            opacity: 1 !important;
            font-family: "Font Awesome 6 Free" !important;
            font-weight: 900 !important;
        }

        .navbar-nav .nav-link span,
        .dropdown-item span {
            display: inline-block !important;
            visibility: visible !important;
            opacity: 1 !important;
            color: inherit !important;
        }

        /* إصلاح خاص لاسم المستخدم */
        .navbar-nav .nav-link:last-child {
            display: flex !important;
            align-items: center !important;
            visibility: visible !important;
            opacity: 1 !important;
            color: rgba(255, 255, 255, 0.9) !important;
        }

        /* إصلاح زر تبديل الوضع المظلم */
        .theme-switch-wrapper,
        .theme-switch {
            display: flex !important;
            align-items: center !important;
            visibility: visible !important;
            opacity: 1 !important;
        }
    </style>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dark-mode.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/medical-theme.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/medical-advanced.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dropdown-fix.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/modal-fix.css') }}">
    {% block styles %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark medical-navbar">
        <div class="container">
            <a class="navbar-brand medical-brand" href="{{ url_for('index') }}">
                <div class="brand-container">
                    <div class="medical-logo">
                        <svg width="30" height="30" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="50" cy="50" r="45" fill="url(#logoGradient)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
                            <rect x="42" y="25" width="16" height="50" fill="white" rx="2"/>
                            <rect x="25" y="42" width="50" height="16" fill="white" rx="2"/>
                            <circle cx="50" cy="50" r="8" fill="rgba(14, 165, 233, 0.8)"/>
                            <defs>
                                <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#0ea5e9;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#0369a1;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                        </svg>
                    </div>
                    <div class="brand-text">
                        <span class="brand-name">العميس الطبية</span>
                        <small class="brand-subtitle">نظام إدارة موظفين المختبر</small>
                    </div>
                </div>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% if session.user_id %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('dashboard') }}">
                                <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                            </a>
                        </li>

                        {% if session.role in ['admin', 'hr', 'gm'] %}
                        <!-- قائمة المستخدمين للإدارة -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="usersDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-users me-1"></i>المستخدمين
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('users') }}"><i class="fas fa-user-friends"></i> قائمة المستخدمين</a></li>
                                {% if session.role in ['admin', 'hr'] %}
                                <li><a class="dropdown-item" href="{{ url_for('new_user') }}"><i class="fas fa-user-plus"></i> إضافة مستخدم جديد</a></li>
                                {% endif %}
                                <li><a class="dropdown-item" href="{{ url_for('departments') }}"><i class="fas fa-building"></i> إدارة الأقسام</a></li>
                            </ul>
                        </li>
                        {% endif %}

                        {% if session.role in ['admin', 'hr', 'manager', 'gm'] %}
                        <!-- قائمة الطلبات المجمعة للإدارة -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="requestsDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-clipboard-check me-1"></i>طلبات الموظفين
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('leave_requests') }}"><i class="fas fa-calendar-alt"></i> طلبات الإجازات</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('coverage_requests') }}"><i class="fas fa-exchange-alt"></i> طلبات التغطية</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('shift_swap_requests') }}"><i class="fas fa-sync-alt"></i> طلبات تبديل الدوام</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('other_requests') }}"><i class="fas fa-clipboard-list"></i> الطلبات الأخرى</a></li>
                            </ul>
                        </li>
                        {% endif %}

                        {% if session.role == 'employee' %}
                        <!-- قائمة الإجازات للموظفين العاديين -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="leavesDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-calendar-alt me-1"></i>الإجازات
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('new_leave') }}"><i class="fas fa-plus-circle"></i> طلب إجازة جديدة</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('my_leaves') }}"><i class="fas fa-list-alt"></i> إجازاتي</a></li>
                            </ul>
                        </li>

                        <!-- قائمة الدوام للموظفين العاديين -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="shiftsDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-calendar-week me-1"></i>جدول الدوام
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('my_shift_schedule') }}"><i class="fas fa-calendar-day"></i> جدول دوامي</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('direct_shift_swap') }}"><i class="fas fa-exchange-alt"></i> تبديل دوام مباشر</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('new_shift_swap') }}"><i class="fas fa-sync-alt"></i> طلب تبديل دوام</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('shift_swap_requests') }}"><i class="fas fa-list-alt"></i> طلبات التبديل</a></li>
                            </ul>
                        </li>

                        <!-- قائمة الطلبات الأخرى للموظفين العاديين -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="otherRequestsDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-clipboard-list me-1"></i>طلبات أخرى
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('new_coverage') }}"><i class="fas fa-exchange-alt"></i> طلب تغطية</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('my_coverage') }}"><i class="fas fa-user-check"></i> تغطياتي</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('new_other_request') }}"><i class="fas fa-plus-circle"></i> طلب آخر</a></li>
                            </ul>
                        </li>

                        <!-- الأذونات للموظفين العاديين -->
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('permissions') }}">
                                <i class="fas fa-clock me-1"></i>الأذونات
                            </a>
                        </li>
                        {% endif %}

                        {% if session.role in ['admin', 'hr', 'manager', 'gm'] %}
                        <!-- الجداول والتقارير للإدارة -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="reportsDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-chart-bar me-1"></i>التقارير والجداول
                            </a>
                            <ul class="dropdown-menu">
                                {% if session.role in ['admin', 'hr', 'gm'] %}
                                <li><a class="dropdown-item" href="{{ url_for('reports') }}"><i class="fas fa-chart-pie"></i> التقارير</a></li>
                                {% endif %}
                                <li><a class="dropdown-item" href="{{ url_for('request_reports') }}"><i class="fas fa-file-alt"></i> تقارير الطلبات</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('schedule') }}"><i class="fas fa-calendar"></i> جدول الإجازات</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('shift_schedule') }}"><i class="fas fa-calendar-week"></i> جدول الشفتات</a></li>
                                {% if session.role in ['admin', 'hr'] %}
                                <li><a class="dropdown-item" href="{{ url_for('leave_balances') }}"><i class="fas fa-calculator"></i> أرصدة الإجازات</a></li>
                                {% endif %}
                                {% if session.role in ['gm'] %}
                                <li><a class="dropdown-item" href="{{ url_for('monthly_operations') }}"><i class="fas fa-cogs"></i> العمليات الشهرية</a></li>
                                {% endif %}
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('interactive_dashboard') }}"><i class="fas fa-chart-pie"></i> لوحة المعلومات التفاعلية</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('interactive_reports') }}"><i class="fas fa-chart-pie"></i> التقارير التفاعلية</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('advanced_reports') }}"><i class="fas fa-chart-line"></i> التقارير المتقدمة</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('export_requests') }}"><i class="fas fa-file-pdf"></i> تصدير الطلبات إلى PDF</a></li>
                            </ul>
                        </li>

                        {% if session.role == 'manager' %}
                        <!-- طلبات تعديل الملفات الشخصية لمدير المختبر -->
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('profile_requests') }}">
                                <i class="fas fa-user-edit me-1"></i>طلبات تعديل الملفات
                            </a>
                        </li>
                        {% endif %}
                        {% endif %}
                    {% endif %}
                </ul>
                <ul class="navbar-nav">
                    <!-- زر تبديل الوضع الداكن -->
                    <li class="nav-item">
                        <div class="theme-switch-wrapper">
                            <label class="theme-switch" for="checkbox">
                                <input type="checkbox" id="checkbox" />
                                <div class="slider">
                                    <i class="fas fa-sun sun-icon"></i>
                                    <i class="fas fa-moon moon-icon"></i>
                                </div>
                            </label>
                        </div>
                    </li>

                    {% if session.user_id %}
                        {% if session.role in ['admin', 'hr', 'manager', 'gm'] %}
                        <!-- زر الإشعارات للمديرين -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-bell me-1"></i>
                                <span class="badge rounded-pill bg-danger" id="notification-badge">0</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end notification-dropdown">
                                <li class="dropdown-header">الإشعارات</li>
                                <li><hr class="dropdown-divider"></li>
                                <li class="notification-item">
                                    <a class="dropdown-item" href="{{ url_for('leave_requests') }}">
                                        <i class="fas fa-calendar-alt text-primary"></i>
                                        <span class="notification-text">طلبات إجازات جديدة تنتظر الموافقة</span>
                                    </a>
                                </li>
                                <li class="notification-item">
                                    <a class="dropdown-item" href="{{ url_for('coverage_requests') }}">
                                        <i class="fas fa-exchange-alt text-success"></i>
                                        <span class="notification-text">طلبات تغطية جديدة تنتظر الموافقة</span>
                                    </a>
                                </li>
                                <li class="notification-item">
                                    <a class="dropdown-item" href="{{ url_for('shift_swap_requests') }}">
                                        <i class="fas fa-sync-alt text-warning"></i>
                                        <span class="notification-text">طلبات تبديل دوام تنتظر الموافقة</span>
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li class="dropdown-item-text text-center">
                                    <a href="{{ url_for('all_notifications') }}" class="btn btn-sm btn-primary w-100">عرض جميع الإشعارات</a>
                                </li>
                            </ul>
                        </li>
                        {% endif %}

                        <!-- المساعدة والدعم -->
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('help_support') }}">
                                <i class="fas fa-question-circle me-1"></i>
                                المساعدة
                            </a>
                        </li>

                        <!-- ملف المستخدم -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle me-1"></i>
                                {{ session.username }}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="{{ url_for('profile') }}"><i class="fas fa-id-card"></i> الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('preferences') }}"><i class="fas fa-cog"></i> الإعدادات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('login') }}">
                                <i class="fas fa-sign-in-alt me-1"></i>تسجيل الدخول
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="medical-footer mt-5">
        <div class="footer-content">
            <div class="container p-4">
                <div class="row">
                    <div class="col-lg-6 col-md-12 mb-4 mb-md-0">
                        <div class="footer-brand">
                            <div class="d-flex align-items-center mb-3">
                                <div class="footer-logo me-3">
                                    <svg width="40" height="40" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="50" cy="50" r="45" fill="url(#footerGradient)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
                                        <rect x="42" y="25" width="16" height="50" fill="white" rx="2"/>
                                        <rect x="25" y="42" width="50" height="16" fill="white" rx="2"/>
                                        <circle cx="50" cy="50" r="8" fill="rgba(14, 165, 233, 0.8)"/>
                                        <defs>
                                            <linearGradient id="footerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                                <stop offset="0%" style="stop-color:#0ea5e9;stop-opacity:1" />
                                                <stop offset="100%" style="stop-color:#0369a1;stop-opacity:1" />
                                            </linearGradient>
                                        </defs>
                                    </svg>
                                </div>
                                <h5 class="footer-title">شركة العميس الطبية</h5>
                            </div>
                            <p class="footer-description">
                                <i class="fas fa-hospital me-2"></i>
                                نظام متكامل لإدارة موظفين المختبر
                            </p>
                            <div class="medical-features">
                                <div class="feature-item">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span>إدارة متقدمة للإجازات</span>
                                </div>
                                <div class="feature-item">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span>تقارير شاملة ومفصلة</span>
                                </div>
                                <div class="feature-item">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span>واجهة سهلة الاستخدام</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-4 mb-md-0">
                        <h5 class="footer-section-title">
                            <i class="fas fa-link me-2"></i>روابط سريعة
                        </h5>
                        <ul class="footer-links">
                            <li><a href="#"><i class="fas fa-book me-2"></i>دليل المستخدم</a></li>
                            <li><a href="#"><i class="fas fa-headset me-2"></i>الدعم الفني</a></li>
                            <li><a href="#"><i class="fas fa-shield-alt me-2"></i>سياسة الخصوصية</a></li>
                            <li><a href="#"><i class="fas fa-file-contract me-2"></i>شروط الاستخدام</a></li>
                        </ul>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-4 mb-md-0">
                        <h5 class="footer-section-title">
                            <i class="fas fa-phone me-2"></i>تواصل معنا
                        </h5>
                        <ul class="footer-contact">
                            <li>
                                <i class="fas fa-envelope text-primary me-2"></i>
                                <span><EMAIL></span>
                            </li>
                            <li>
                                <i class="fas fa-phone text-success me-2"></i>
                                <span>+966 11 123 4567</span>
                            </li>
                            <li>
                                <i class="fas fa-map-marker-alt text-danger me-2"></i>
                                <span>الرياض، المملكة العربية السعودية</span>
                            </li>
                            <li>
                                <i class="fas fa-clock text-warning me-2"></i>
                                <span>24/7 دعم فني</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="mb-0">
                            <i class="fas fa-copyright me-1"></i>
                            2024 ALEMEIS - جميع الحقوق محفوظة
                        </p>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="footer-badges">
                            <span class="badge bg-success me-2">
                                <i class="fas fa-shield-alt me-1"></i>آمن
                            </span>
                            <span class="badge bg-primary me-2">
                                <i class="fas fa-mobile-alt me-1"></i>متجاوب
                            </span>
                            <span class="badge bg-info">
                                <i class="fas fa-cloud me-1"></i>سحابي
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- ALEMEIS System JS -->
    <script src="{{ url_for('static', filename='js/alemeis-system.js') }}"></script>

    <!-- Dropdown Fix JS -->
    <script src="{{ url_for('static', filename='js/dropdown-fix.js') }}"></script>

    <!-- Modal Fix JS -->
    <script src="{{ url_for('static', filename='js/modal-fix.js') }}"></script>

    <!-- Data Display Fix JS -->
    <script src="{{ url_for('static', filename='js/data-display-fix.js') }}"></script>

    <!-- Dropdown Position Fix JS -->
    <script src="{{ url_for('static', filename='js/dropdown-position-fix.js') }}"></script>

    {% block scripts %}{% endblock %}
</body>
</html>
