import sqlite3
from datetime import datetime

# Connect to the database
conn = sqlite3.connect('alemis.db')
conn.row_factory = sqlite3.Row
cursor = conn.cursor()

# Get current year
current_year = datetime.now().year

# Get all users
cursor.execute("SELECT id FROM users")
users = cursor.fetchall()

# Get leave types
cursor.execute("SELECT id, name, default_days FROM leave_types")
leave_types = cursor.fetchall()

# Create leave balances for each user
for user in users:
    user_id = user['id']
    
    for leave_type in leave_types:
        leave_type_id = leave_type['id']
        leave_type_name = leave_type['name']
        default_days = leave_type['default_days']
        
        # Skip leave types that don't have default balances
        if leave_type_name in ['إجازة بدون راتب', 'إجازة تغطية']:
            continue
        
        # Check if balance already exists
        cursor.execute('''
            SELECT * FROM leave_balances
            WHERE user_id = ? AND leave_type_id = ? AND year = ?
        ''', (user_id, leave_type_id, current_year))
        
        if not cursor.fetchone():
            # Insert leave balance
            cursor.execute('''
                INSERT INTO leave_balances
                (user_id, leave_type_id, year, total_days, used_days, remaining_days)
                VALUES (?, ?, ?, ?, 0, ?)
            ''', (user_id, leave_type_id, current_year, default_days, default_days))
            
            print(f"Created leave balance for user {user_id}, leave type {leave_type_name}")

# Commit changes
conn.commit()

# Close the connection
conn.close()

print("Leave balances created successfully")
