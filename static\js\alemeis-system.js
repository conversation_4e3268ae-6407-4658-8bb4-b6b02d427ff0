/**
 * ALEMEIS - نظام إدارة إجازات الموظفين في المختبرات
 * ملف JavaScript الرئيسي - مبسط وفعال
 */

document.addEventListener('DOMContentLoaded', function() {
    // إصلاح مشكلة Font Awesome
    fixFontAwesome();

    // تحقق من تحميل الأيقونات
    checkIconsLoaded();
    console.log('🏥 ALEMEIS System Loading...');

    // التحقق من تحميل Bootstrap
    if (typeof bootstrap === 'undefined') {
        console.error('❌ Bootstrap is not loaded!');
        showErrorMessage('خطأ في تحميل النظام. يرجى إعادة تحميل الصفحة.');
        return;
    }

    console.log('✅ Bootstrap loaded successfully');

    // تفعيل جميع مكونات Bootstrap
    initializeBootstrapComponents();

    // إصلاح الشريط العلوي
    fixNavbar();

    // تفعيل الوضع المظلم
    initializeDarkMode();

    // تفعيل النماذج
    initializeForms();

    // تفعيل حساب أيام الإجازة
    initializeLeaveDaysCalculation();

    // تفعيل تأكيد الحذف
    initializeConfirmDialogs();

    // إخفاء التنبيهات تلقائياً
    autoHideAlerts();

    // تفعيل زر إظهار/إخفاء كلمة المرور
    initializePasswordToggle();

    console.log('🚀 ALEMEIS System fully loaded and ready!');
});

// تفعيل مكونات Bootstrap
function initializeBootstrapComponents() {
    try {
        // 1. تفعيل القوائم المنسدلة مع إعدادات محسنة
        var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
        var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
            // إزالة أي dropdown موجود مسبقاً
            var existingDropdown = bootstrap.Dropdown.getInstance(dropdownToggleEl);
            if (existingDropdown) {
                existingDropdown.dispose();
            }

            // إنشاء dropdown جديد مع إعدادات محسنة
            return new bootstrap.Dropdown(dropdownToggleEl, {
                autoClose: true,
                boundary: 'clippingParents',
                display: 'dynamic',
                offset: [0, 2],
                popperConfig: null,
                reference: 'toggle'
            });
        });
        console.log('✅ Dropdowns initialized:', dropdownList.length);

        // 2. تفعيل النوافذ المنبثقة
        var modalElementList = [].slice.call(document.querySelectorAll('.modal'));
        var modalList = modalElementList.map(function (modalEl) {
            return new bootstrap.Modal(modalEl, {
                backdrop: true,
                keyboard: true,
                focus: true
            });
        });
        console.log('✅ Modals initialized:', modalList.length);

        // 3. تفعيل التلميحات
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
        console.log('✅ Tooltips initialized:', tooltipList.length);

        // 4. تفعيل النوافذ المنبثقة الصغيرة
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
        console.log('✅ Popovers initialized:', popoverList.length);

        // 5. تفعيل التنبيهات
        var alertList = document.querySelectorAll('.alert .btn-close');
        alertList.forEach(function (alert) {
            new bootstrap.Alert(alert.parentElement);
        });
        console.log('✅ Alerts initialized:', alertList.length);

    } catch (error) {
        console.error('❌ Error initializing Bootstrap components:', error);
    }
}

// تفعيل الوضع المظلم
function initializeDarkMode() {
    try {
        const currentTheme = localStorage.getItem('theme') || 'light';
        const toggleSwitch = document.querySelector('#checkbox');

        if (toggleSwitch) {
            document.documentElement.setAttribute('data-theme', currentTheme);

            if (currentTheme === 'dark') {
                toggleSwitch.checked = true;
            }

            toggleSwitch.addEventListener('change', function(e) {
                if (e.target.checked) {
                    document.documentElement.setAttribute('data-theme', 'dark');
                    localStorage.setItem('theme', 'dark');
                } else {
                    document.documentElement.setAttribute('data-theme', 'light');
                    localStorage.setItem('theme', 'light');
                }
            });

            console.log('✅ Dark mode initialized');
        }
    } catch (error) {
        console.error('❌ Error initializing dark mode:', error);
    }
}

// تفعيل النماذج
function initializeForms() {
    try {
        // تفعيل التحقق من صحة النماذج
        var forms = document.querySelectorAll('.needs-validation');
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });

        // تحسين تفاعل حقول الإدخال
        const formControls = document.querySelectorAll('.form-control, .form-select');
        formControls.forEach(control => {
            control.addEventListener('focus', function() {
                const parent = this.closest('.mb-3, .form-group');
                if (parent) parent.classList.add('focused');
            });

            control.addEventListener('blur', function() {
                const parent = this.closest('.mb-3, .form-group');
                if (parent) parent.classList.remove('focused');
            });
        });

        console.log('✅ Forms initialized:', forms.length);
    } catch (error) {
        console.error('❌ Error initializing forms:', error);
    }
}

// حساب أيام الإجازة
function initializeLeaveDaysCalculation() {
    try {
        const startDateInput = document.querySelector('#start_date');
        const endDateInput = document.querySelector('#end_date');

        if (startDateInput && endDateInput) {
            function calculateDays() {
                if (startDateInput.value && endDateInput.value) {
                    const startDate = new Date(startDateInput.value);
                    const endDate = new Date(endDateInput.value);

                    if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
                        const timeDiff = endDate.getTime() - startDate.getTime();
                        const dayDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;

                        let daysInfo = document.getElementById('days-info');
                        if (!daysInfo) {
                            daysInfo = document.createElement('div');
                            daysInfo.id = 'days-info';
                            daysInfo.className = 'alert alert-primary mt-3';
                            endDateInput.parentNode.appendChild(daysInfo);
                        }

                        if (dayDiff > 0) {
                            daysInfo.innerHTML = '<i class="fas fa-calendar-day me-1"></i> عدد أيام الإجازة: <strong>' + dayDiff + '</strong> يوم';
                            daysInfo.className = 'alert alert-primary mt-3';
                        } else {
                            daysInfo.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i> تاريخ النهاية يجب أن يكون بعد تاريخ البداية';
                            daysInfo.className = 'alert alert-warning mt-3';
                        }
                    }
                }
            }

            startDateInput.addEventListener('change', calculateDays);
            endDateInput.addEventListener('change', calculateDays);

            console.log('✅ Leave days calculation initialized');
        }
    } catch (error) {
        console.error('❌ Error initializing leave days calculation:', error);
    }
}

// تأكيد الحذف
function initializeConfirmDialogs() {
    try {
        const confirmButtons = document.querySelectorAll('[data-confirm]');
        confirmButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                if (!confirm(button.dataset.confirm)) {
                    e.preventDefault();
                }
            });
        });

        console.log('✅ Confirm dialogs initialized:', confirmButtons.length);
    } catch (error) {
        console.error('❌ Error initializing confirm dialogs:', error);
    }
}

// إخفاء التنبيهات تلقائياً
function autoHideAlerts() {
    try {
        setTimeout(function() {
            var alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
            alerts.forEach(function(alert) {
                if (alert.querySelector('.btn-close')) {
                    var bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            });
        }, 5000);

        console.log('✅ Auto-hide alerts initialized');
    } catch (error) {
        console.error('❌ Error with auto-hide alerts:', error);
    }
}

// تفعيل زر إظهار/إخفاء كلمة المرور
function initializePasswordToggle() {
    try {
        const toggleButton = document.getElementById('togglePassword');
        const passwordInput = document.getElementById('inputPassword');

        if (toggleButton && passwordInput) {
            toggleButton.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);

                const icon = this.querySelector('i');
                if (type === 'password') {
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                } else {
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                }
            });

            console.log('✅ Password toggle initialized');
        }
    } catch (error) {
        console.error('❌ Error initializing password toggle:', error);
    }
}

// إصلاح مشكلة Font Awesome
function fixFontAwesome() {
    try {
        // إضافة CSS إضافي لضمان ظهور الأيقونات
        const style = document.createElement('style');
        style.textContent = `
            .fas, .far, .fab, .fal, .fad, .fa {
                font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "Font Awesome 5 Free", "Font Awesome 5 Pro", "FontAwesome" !important;
                font-weight: 900 !important;
                font-style: normal !important;
                font-variant: normal !important;
                text-transform: none !important;
                line-height: 1 !important;
                -webkit-font-smoothing: antialiased !important;
                -moz-osx-font-smoothing: grayscale !important;
                display: inline-block !important;
                visibility: visible !important;
                opacity: 1 !important;
            }

            .far {
                font-weight: 400 !important;
            }

            .fal {
                font-weight: 300 !important;
            }

            /* إصلاح خاص للشريط العلوي */
            .navbar .fas, .navbar .far, .navbar .fab,
            .dropdown-menu .fas, .dropdown-menu .far, .dropdown-menu .fab {
                display: inline-block !important;
                visibility: visible !important;
                opacity: 1 !important;
                width: auto !important;
                height: auto !important;
            }
        `;
        document.head.appendChild(style);
        console.log('✅ Font Awesome CSS fix applied');
    } catch (error) {
        console.error('❌ Error applying Font Awesome fix:', error);
    }
}

// تحقق من تحميل الأيقونات
function checkIconsLoaded() {
    try {
        setTimeout(() => {
            const testIcon = document.querySelector('.fas, .far, .fab');
            if (testIcon) {
                const computedStyle = window.getComputedStyle(testIcon, '::before');
                const content = computedStyle.getPropertyValue('content');

                if (content && content !== 'none' && content !== '""') {
                    console.log('✅ Font Awesome icons loaded successfully');

                    // إضافة فئة للتأكد من ظهور الأيقونات
                    document.querySelectorAll('.fas, .far, .fab, .fal, .fad, .fa').forEach(icon => {
                        icon.style.display = 'inline-block';
                        icon.style.visibility = 'visible';
                        icon.style.opacity = '1';
                    });
                } else {
                    console.warn('⚠️ Font Awesome icons may not be loaded properly');
                    loadFallbackIcons();
                }
            } else {
                console.warn('⚠️ No Font Awesome icons found on page');
            }
        }, 1000);
    } catch (error) {
        console.error('❌ Error checking icons:', error);
    }
}

// تحميل أيقونات احتياطية
function loadFallbackIcons() {
    try {
        // إضافة رابط احتياطي لـ Font Awesome
        const fallbackLink = document.createElement('link');
        fallbackLink.rel = 'stylesheet';
        fallbackLink.href = 'https://use.fontawesome.com/releases/v6.5.1/css/all.css';
        fallbackLink.crossOrigin = 'anonymous';
        document.head.appendChild(fallbackLink);

        console.log('🔄 Loading fallback Font Awesome...');

        // تحقق مرة أخرى بعد تحميل الاحتياطي
        setTimeout(() => {
            checkIconsLoaded();
        }, 2000);
    } catch (error) {
        console.error('❌ Error loading fallback icons:', error);
    }
}

// إصلاح الشريط العلوي
function fixNavbar() {
    try {
        // انتظار تحميل DOM كاملاً
        setTimeout(() => {
            // التأكد من ظهور جميع عناصر الشريط العلوي
            const navbar = document.querySelector('.navbar');
            const navbarNav = document.querySelector('#navbarNav');
            const navLinks = document.querySelectorAll('.nav-link');
            const dropdowns = document.querySelectorAll('.dropdown-toggle');

            if (navbar) {
                navbar.style.display = 'block';
                navbar.style.visibility = 'visible';
            }

            if (navbarNav) {
                navbarNav.style.display = 'flex';
                navbarNav.style.visibility = 'visible';
            }

            // إصلاح روابط التنقل
            navLinks.forEach(link => {
                link.style.display = 'flex';
                link.style.alignItems = 'center';
                link.style.visibility = 'visible';

                // إصلاح الأيقونات داخل الروابط
                const icons = link.querySelectorAll('.fas, .far, .fab');
                icons.forEach(icon => {
                    icon.style.display = 'inline-block';
                    icon.style.visibility = 'visible';
                    icon.style.opacity = '1';
                });
            });

            // إصلاح القوائم المنسدلة بطريقة محسنة
            dropdowns.forEach(dropdown => {
                // إزالة أي مستمعين موجودين
                dropdown.removeEventListener('click', handleDropdownClick);

                // إضافة مستمع جديد
                dropdown.addEventListener('click', handleDropdownClick);

                // التأكد من وجود الخصائص المطلوبة
                dropdown.setAttribute('data-bs-toggle', 'dropdown');
                dropdown.setAttribute('aria-expanded', 'false');
            });

            // إضافة معالج للنقر خارج القوائم
            document.addEventListener('click', handleOutsideClick);

            console.log('✅ Navbar fixed successfully');
        }, 500);

    } catch (error) {
        console.error('❌ Error fixing navbar:', error);
    }
}

// معالج النقر على القوائم المنسدلة
function handleDropdownClick(e) {
    e.preventDefault();
    e.stopPropagation();

    const dropdown = e.currentTarget;
    const menu = dropdown.nextElementSibling;

    if (menu && menu.classList.contains('dropdown-menu')) {
        // إغلاق جميع القوائم الأخرى
        document.querySelectorAll('.dropdown-menu.show').forEach(otherMenu => {
            if (otherMenu !== menu) {
                otherMenu.classList.remove('show');
                const otherDropdown = otherMenu.previousElementSibling;
                if (otherDropdown) {
                    otherDropdown.setAttribute('aria-expanded', 'false');
                }
            }
        });

        // تبديل حالة القائمة الحالية
        const isOpen = menu.classList.contains('show');
        if (isOpen) {
            menu.classList.remove('show');
            dropdown.setAttribute('aria-expanded', 'false');
        } else {
            menu.classList.add('show');
            dropdown.setAttribute('aria-expanded', 'true');
        }
    }
}

// معالج النقر خارج القوائم
function handleOutsideClick(e) {
    if (!e.target.closest('.dropdown')) {
        document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
            menu.classList.remove('show');
            const dropdown = menu.previousElementSibling;
            if (dropdown) {
                dropdown.setAttribute('aria-expanded', 'false');
            }
        });
    }
}

// عرض رسالة خطأ
function showErrorMessage(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);
}
