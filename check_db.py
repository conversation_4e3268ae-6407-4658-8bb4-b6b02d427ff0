import sqlite3

# Connect to the database
conn = sqlite3.connect('alemis.db')
conn.row_factory = sqlite3.Row
cursor = conn.cursor()

# Check if users table exists
cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
if cursor.fetchone():
    print("Users table exists")
    
    # Check if there are any users
    cursor.execute("SELECT COUNT(*) as count FROM users")
    count = cursor.fetchone()['count']
    print(f"Number of users: {count}")
    
    if count > 0:
        # Get all users
        cursor.execute("SELECT id, username, email, first_name, last_name, role, password_hash FROM users")
        users = cursor.fetchall()
        
        print("\nUsers:")
        for user in users:
            print(f"ID: {user['id']}, Username: {user['username']}, Email: {user['email']}, Name: {user['first_name']} {user['last_name']}, Role: {user['role']}, Password Hash: {user['password_hash']}")
else:
    print("Users table does not exist")

# Check if leave_types table exists
cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='leave_types'")
if cursor.fetchone():
    print("\nLeave types table exists")
    
    # Get all leave types
    cursor.execute("SELECT id, name, description, default_days FROM leave_types")
    leave_types = cursor.fetchall()
    
    print("\nLeave Types:")
    for leave_type in leave_types:
        print(f"ID: {leave_type['id']}, Name: {leave_type['name']}, Description: {leave_type['description']}, Default Days: {leave_type['default_days']}")
else:
    print("\nLeave types table does not exist")

# Check if departments table exists
cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='departments'")
if cursor.fetchone():
    print("\nDepartments table exists")
    
    # Get all departments
    cursor.execute("SELECT id, name, description FROM departments")
    departments = cursor.fetchall()
    
    print("\nDepartments:")
    for department in departments:
        print(f"ID: {department['id']}, Name: {department['name']}, Description: {department['description']}")
else:
    print("\nDepartments table does not exist")

# Close the connection
conn.close()
