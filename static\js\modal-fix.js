/**
 * إصلاح شامل للنوافذ المنبثقة - ALEMEIS System
 * حل جميع مشاكل النوافذ المنبثقة (Modals) في النظام
 */

(function() {
    'use strict';

    let isInitialized = false;

    // انتظار تحميل DOM
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🔧 بدء إصلاح النوافذ المنبثقة...');
        
        // تأخير قصير للتأكد من تحميل Bootstrap
        setTimeout(initializeModals, 500);
    });

    function initializeModals() {
        if (isInitialized) return;
        
        try {
            console.log('🚀 تهيئة النوافذ المنبثقة...');
            
            // البحث عن جميع النوافذ المنبثقة
            const modals = document.querySelectorAll('.modal');
            console.log(`📋 تم العثور على ${modals.length} نافذة منبثقة`);

            modals.forEach((modal, index) => {
                setupModal(modal, index);
            });

            // إعداد أزرار فتح النوافذ
            setupModalTriggers();

            // إضافة مستمعي الأحداث العامة
            setupGlobalEventListeners();

            isInitialized = true;
            console.log('✅ تم إكمال إصلاح النوافذ المنبثقة بنجاح');

        } catch (error) {
            console.error('❌ خطأ في تهيئة النوافذ المنبثقة:', error);
        }
    }

    function setupModal(modal, index) {
        console.log(`🔧 إعداد النافذة ${index + 1}:`, modal.id);

        try {
            // إزالة أي instance موجود مسبقاً
            const existingModal = bootstrap.Modal.getInstance(modal);
            if (existingModal) {
                existingModal.dispose();
            }

            // إنشاء instance جديد مع إعدادات محسنة
            const modalInstance = new bootstrap.Modal(modal, {
                backdrop: true,
                keyboard: true,
                focus: true
            });

            // إضافة مستمعي الأحداث للنافذة
            modal.addEventListener('show.bs.modal', function(e) {
                console.log('📂 فتح النافذة:', this.id);
                handleModalShow(this, e);
            });

            modal.addEventListener('shown.bs.modal', function(e) {
                console.log('✅ تم فتح النافذة:', this.id);
                handleModalShown(this, e);
            });

            modal.addEventListener('hide.bs.modal', function(e) {
                console.log('📁 إغلاق النافذة:', this.id);
                handleModalHide(this, e);
            });

            modal.addEventListener('hidden.bs.modal', function(e) {
                console.log('❌ تم إغلاق النافذة:', this.id);
                handleModalHidden(this, e);
            });

            // إضافة أنماط أساسية للنافذة
            styleModal(modal);
            
            console.log(`✅ تم إعداد النافذة ${index + 1} بنجاح`);

        } catch (error) {
            console.error(`❌ خطأ في إعداد النافذة ${index + 1}:`, error);
        }
    }

    function setupModalTriggers() {
        // البحث عن جميع أزرار فتح النوافذ
        const triggers = document.querySelectorAll('[data-bs-toggle="modal"]');
        console.log(`🔘 تم العثور على ${triggers.length} زر لفتح النوافذ`);

        triggers.forEach((trigger, index) => {
            console.log(`🔧 إعداد الزر ${index + 1}:`, trigger);

            // إزالة أي مستمعين موجودين
            const newTrigger = trigger.cloneNode(true);
            trigger.parentNode.replaceChild(newTrigger, trigger);

            // إضافة مستمع النقر الجديد
            newTrigger.addEventListener('click', function(e) {
                handleTriggerClick(e, this);
            });
        });
    }

    function handleTriggerClick(event, trigger) {
        event.preventDefault();
        event.stopPropagation();

        const targetId = trigger.getAttribute('data-bs-target');
        if (!targetId) {
            console.warn('⚠️ لم يتم تحديد هدف النافذة');
            return;
        }

        const targetModal = document.querySelector(targetId);
        if (!targetModal) {
            console.warn('⚠️ لم يتم العثور على النافذة المستهدفة:', targetId);
            return;
        }

        console.log('🖱️ تم النقر على زر فتح النافذة:', targetId);

        try {
            // الحصول على instance النافذة
            let modalInstance = bootstrap.Modal.getInstance(targetModal);
            
            if (!modalInstance) {
                modalInstance = new bootstrap.Modal(targetModal, {
                    backdrop: true,
                    keyboard: true,
                    focus: true
                });
            }

            // فتح النافذة
            modalInstance.show();
            console.log('✅ تم فتح النافذة بنجاح');

        } catch (error) {
            console.error('❌ خطأ في فتح النافذة:', error);
        }
    }

    function handleModalShow(modal, event) {
        // إضافة تأثيرات عند بداية فتح النافذة
        modal.style.display = 'block';
        
        // إضافة فئة للتحكم في التأثيرات
        modal.classList.add('modal-opening');
        
        // التأكد من وضع النافذة في المقدمة
        modal.style.zIndex = '1055';
    }

    function handleModalShown(modal, event) {
        // إزالة فئة الفتح وإضافة فئة المفتوح
        modal.classList.remove('modal-opening');
        modal.classList.add('modal-opened');
        
        // التركيز على أول عنصر قابل للتفاعل
        const firstInput = modal.querySelector('input, textarea, select, button');
        if (firstInput) {
            setTimeout(() => {
                firstInput.focus();
            }, 100);
        }
    }

    function handleModalHide(modal, event) {
        // إضافة تأثيرات عند بداية إغلاق النافذة
        modal.classList.remove('modal-opened');
        modal.classList.add('modal-closing');
    }

    function handleModalHidden(modal, event) {
        // تنظيف بعد إغلاق النافذة
        modal.classList.remove('modal-closing');
        modal.style.display = 'none';
        
        // إزالة أي backdrop متبقي
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => {
            if (backdrop.parentNode) {
                backdrop.parentNode.removeChild(backdrop);
            }
        });
        
        // إزالة فئة modal-open من body إذا لم تعد هناك نوافذ مفتوحة
        const openModals = document.querySelectorAll('.modal.show');
        if (openModals.length === 0) {
            document.body.classList.remove('modal-open');
            document.body.style.paddingRight = '';
        }
    }

    function styleModal(modal) {
        // إضافة أنماط أساسية للنافذة المنبثقة
        modal.style.zIndex = '1050';
        
        // تحسين مظهر النافذة
        const modalDialog = modal.querySelector('.modal-dialog');
        if (modalDialog) {
            modalDialog.style.margin = '1.75rem auto';
            modalDialog.style.maxWidth = '500px';
        }
        
        const modalContent = modal.querySelector('.modal-content');
        if (modalContent) {
            modalContent.style.borderRadius = '8px';
            modalContent.style.border = 'none';
            modalContent.style.boxShadow = '0 10px 25px rgba(0,0,0,0.2)';
        }
    }

    function setupGlobalEventListeners() {
        // إغلاق النوافذ بمفتاح Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const openModals = document.querySelectorAll('.modal.show');
                openModals.forEach(modal => {
                    const modalInstance = bootstrap.Modal.getInstance(modal);
                    if (modalInstance) {
                        modalInstance.hide();
                    }
                });
            }
        });

        // معالجة النقر على الخلفية
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal')) {
                const modalInstance = bootstrap.Modal.getInstance(e.target);
                if (modalInstance) {
                    modalInstance.hide();
                }
            }
        });
    }

    // تصدير الدوال للاستخدام الخارجي
    window.ModalFix = {
        init: initializeModals,
        reinit: function() {
            isInitialized = false;
            initializeModals();
        },
        openModal: function(modalId) {
            const modal = document.querySelector(modalId);
            if (modal) {
                const modalInstance = bootstrap.Modal.getInstance(modal) || new bootstrap.Modal(modal);
                modalInstance.show();
            }
        },
        closeModal: function(modalId) {
            const modal = document.querySelector(modalId);
            if (modal) {
                const modalInstance = bootstrap.Modal.getInstance(modal);
                if (modalInstance) {
                    modalInstance.hide();
                }
            }
        }
    };

    console.log('📦 تم تحميل وحدة إصلاح النوافذ المنبثقة');
})();
