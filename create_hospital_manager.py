#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import hashlib
from datetime import datetime

def create_hospital_manager():
    """إنشاء حساب مدير المستشفى"""
    
    conn = sqlite3.connect('alemis.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        print("🏥 إنشاء حساب مدير المستشفى...")
        
        # Check if hospital_manager role exists
        cursor.execute('SELECT * FROM users WHERE role = ?', ('hospital_manager',))
        existing = cursor.fetchone()
        
        if existing:
            print("✅ حساب مدير المستشفى موجود بالفعل")
            print(f"   اسم المستخدم: {existing['username']}")
            return
        
        # Hash password
        password = 'hospital123'
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        
        # Get first department
        cursor.execute('SELECT * FROM departments LIMIT 1')
        department = cursor.fetchone()
        dept_id = department['id'] if department else None
        
        # Create hospital manager account
        cursor.execute('''
            INSERT INTO users (username, password, first_name, last_name, email, phone, role, department_id, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', ('hospital_manager', password_hash, 'مدير', 'المستشفى', '<EMAIL>', 
              '**********', 'hospital_manager', dept_id, datetime.now()))
        
        conn.commit()
        
        print("✅ تم إنشاء حساب مدير المستشفى بنجاح!")
        print("📋 بيانات الحساب:")
        print("   اسم المستخدم: hospital_manager")
        print("   كلمة المرور: hospital123")
        print("   الدور: مدير المستشفى")
        print("   الإيميل: <EMAIL>")
        
    except Exception as e:
        print(f'❌ خطأ: {e}')
        conn.rollback()
    finally:
        conn.close()

if __name__ == '__main__':
    create_hospital_manager()
