/* إصلاح شامل للقوائم المنسدلة - ALEMEIS System */

/* إعدادات أساسية للقوائم المنسدلة */
.dropdown {
    position: relative !important;
}

.dropdown-toggle {
    cursor: pointer !important;
    user-select: none !important;
    transition: all 0.15s ease !important;
    border: none !important;
    background: none !important;
    outline: none !important;
}

.dropdown-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-radius: 6px !important;
}

.dropdown-toggle.show {
    background-color: rgba(255, 255, 255, 0.15) !important;
    border-radius: 6px !important;
}

.dropdown-menu {
    display: none !important;
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: auto !important;
    z-index: 1055 !important;
    min-width: 200px !important;
    max-width: 350px !important;
    padding: 8px 0 !important;
    margin: 2px 0 0 0 !important;
    font-size: 0.9rem !important;
    color: #212529 !important;
    text-align: right !important;
    list-style: none !important;
    background-color: #ffffff !important;
    background-clip: padding-box !important;
    border: 1px solid rgba(0,0,0,.12) !important;
    border-radius: 8px !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    transform: translateY(0) !important;
    transition: opacity 0.15s ease, transform 0.15s ease !important;
    white-space: nowrap !important;
    pointer-events: auto !important;
    visibility: visible !important;
    opacity: 1 !important;
    overflow: visible !important;
    max-height: none !important;
}

/* تأثيرات الحركة المحسنة */
@keyframes dropdownSlideIn {
    from {
        opacity: 0;
        transform: translateY(-5px) scale(0.98);
        visibility: visible;
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
        visibility: visible;
    }
}

@keyframes dropdownSlideOut {
    from {
        opacity: 1;
        transform: translateY(0) scale(1);
        visibility: visible;
    }
    to {
        opacity: 0;
        transform: translateY(-5px) scale(0.98);
        visibility: hidden;
    }
}

/* عرض القائمة المنسدلة */
.dropdown-menu.show {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    pointer-events: auto !important;
    animation: dropdownSlideIn 0.15s ease-out !important;
}

.dropdown-menu.hiding {
    animation: dropdownSlideOut 0.15s ease-in !important;
}

/* موقع القوائم في الجانب الأيمن */
.dropdown-menu-end {
    right: 0 !important;
    left: auto !important;
}

/* إصلاح خاص للقوائم في الشريط العلوي */
.navbar .dropdown-menu {
    position: absolute !important;
    top: calc(100% + 2px) !important;
    left: 0 !important;
    right: auto !important;
    z-index: 1055 !important;
    transform: none !important;
}

/* إصلاح للقوائم المنسدلة في الجانب الأيمن من الشريط */
.navbar-nav:last-child .dropdown-menu,
.navbar-nav .dropdown-menu-end {
    position: absolute !important;
    right: 0 !important;
    left: auto !important;
    top: calc(100% + 2px) !important;
}

/* إصلاح للقوائم المنسدلة في الجانب الأيسر من الشريط */
.navbar-nav:first-child .dropdown-menu {
    position: absolute !important;
    left: 0 !important;
    right: auto !important;
    top: calc(100% + 2px) !important;
}

/* عناصر القائمة المنسدلة */
.dropdown-item {
    display: flex !important;
    align-items: center !important;
    width: 100% !important;
    padding: 12px 16px !important;
    clear: both !important;
    font-weight: 500 !important;
    color: #374151 !important;
    text-align: right !important;
    text-decoration: none !important;
    white-space: nowrap !important;
    background-color: transparent !important;
    border: 0 !important;
    transition: all 0.2s ease !important;
    border-radius: 6px !important;
    margin: 2px 8px !important;
    visibility: visible !important;
    opacity: 1 !important;
    font-size: 0.95rem !important;
    line-height: 1.5 !important;
    cursor: pointer !important;
}

.dropdown-item:hover,
.dropdown-item:focus {
    color: #0ea5e9 !important;
    background-color: rgba(14, 165, 233, 0.08) !important;
    transform: translateX(-2px) !important;
    text-decoration: none !important;
    box-shadow: 0 2px 4px rgba(14, 165, 233, 0.1) !important;
}

.dropdown-item:active {
    color: #ffffff !important;
    background-color: #0ea5e9 !important;
    transform: translateX(-1px) !important;
}

/* أيقونات عناصر القائمة */
.dropdown-item i {
    width: 18px !important;
    margin-left: 10px !important;
    text-align: center !important;
    font-size: 0.9rem !important;
    flex-shrink: 0 !important;
    color: #6b7280 !important;
    transition: color 0.2s ease !important;
}

.dropdown-item:hover i,
.dropdown-item:focus i {
    color: #0ea5e9 !important;
}

/* فواصل القائمة */
.dropdown-divider {
    height: 0 !important;
    margin: 0.5rem 0 !important;
    overflow: hidden !important;
    border-top: 1px solid #e9ecef !important;
}

/* عناوين القائمة */
.dropdown-header {
    display: block !important;
    padding: 0.5rem 1.5rem !important;
    margin-bottom: 0 !important;
    font-size: 0.875rem !important;
    color: #6b7280 !important;
    white-space: nowrap !important;
    font-weight: 600 !important;
}

/* سهم القائمة المنسدلة */
.dropdown-toggle::after {
    display: inline-block !important;
    margin-right: 0.255em !important;
    vertical-align: 0.255em !important;
    content: "" !important;
    border-top: 0.3em solid !important;
    border-left: 0.3em solid transparent !important;
    border-bottom: 0 !important;
    border-right: 0.3em solid transparent !important;
}

/* إصلاحات خاصة بالشريط العلوي */
.navbar .dropdown {
    position: relative !important;
}

.navbar .dropdown-menu {
    position: absolute !important;
    z-index: 1055 !important;
}

.navbar .container {
    position: relative !important;
    overflow: visible !important;
}

.navbar-collapse {
    overflow: visible !important;
}

.navbar-nav {
    overflow: visible !important;
}

.nav-item {
    overflow: visible !important;
    position: relative !important;
}

/* إصلاح شامل للقوائم المنسدلة */
body {
    overflow-x: visible !important;
    overflow-y: auto !important;
}

.container,
.container-fluid {
    overflow: visible !important;
    position: relative !important;
}

/* إصلاح خاص للشريط العلوي */
.navbar {
    overflow: visible !important;
    position: relative !important;
    z-index: 1030 !important;
}

.navbar .container {
    overflow: visible !important;
    position: relative !important;
}

.navbar-collapse {
    overflow: visible !important;
    position: relative !important;
}

.navbar-nav {
    overflow: visible !important;
    position: relative !important;
}

.nav-item {
    overflow: visible !important;
    position: relative !important;
}

/* إصلاح خاص للقوائم في الشريط العلوي */
.navbar .dropdown-menu {
    position: absolute !important;
    z-index: 1055 !important;
    min-width: 220px !important;
    max-width: 350px !important;
    background: white !important;
    border: 1px solid rgba(0,0,0,0.12) !important;
    border-radius: 8px !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    padding: 8px 0 !important;
    margin: 2px 0 0 0 !important;
    overflow: visible !important;
    max-height: none !important;
    width: auto !important;
    transform: none !important;
}

/* سهم القائمة المنسدلة */
.dropdown-arrow {
    position: absolute !important;
    top: -8px !important;
    width: 16px !important;
    height: 16px !important;
    background: white !important;
    border: 1px solid rgba(0,0,0,0.15) !important;
    border-bottom: none !important;
    border-right: none !important;
    transform: rotate(45deg) !important;
    z-index: 1 !important;
}

/* تحسين شكل القائمة مع السهم */
.navbar .dropdown-menu {
    margin-top: 0 !important;
    border-top-left-radius: 12px !important;
    border-top-right-radius: 12px !important;
}

/* إصلاح للقوائم المنسدلة المختلفة */
.navbar .dropdown-menu:not(.dropdown-menu-end) {
    transform-origin: top left !important;
}

.navbar .dropdown-menu.dropdown-menu-end {
    transform-origin: top right !important;
}

/* إصلاح النصوص والأيقونات في الشريط العلوي */
.navbar-nav .nav-link {
    display: flex !important;
    align-items: center !important;
    color: rgba(255, 255, 255, 0.9) !important;
    text-decoration: none !important;
    font-weight: 500 !important;
    padding: 8px 12px !important;
    border-radius: 6px !important;
    margin: 0 4px !important;
    transition: all 0.2s ease !important;
    visibility: visible !important;
    opacity: 1 !important;
    white-space: nowrap !important;
    position: relative !important;
}

.navbar-nav .nav-link:hover {
    color: #ffffff !important;
    background: rgba(255, 255, 255, 0.1) !important;
    text-decoration: none !important;
    transform: translateY(-1px) !important;
}

.navbar-nav .nav-link i {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    margin-left: 8px !important;
    font-size: 0.95rem !important;
    transition: transform 0.2s ease !important;
}

.navbar-nav .nav-link:hover i {
    transform: scale(1.1) !important;
}

/* إصلاح خاص للنصوص */
.navbar-nav .nav-link span,
.dropdown-item span {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    color: inherit !important;
    font-size: inherit !important;
    line-height: inherit !important;
}

/* إصلاح النصوص في القوائم المنسدلة */
.dropdown-item span,
.dropdown-item .notification-text {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    color: inherit !important;
    font-size: inherit !important;
    line-height: inherit !important;
}

/* إصلاح اسم المستخدم */
.navbar-nav .nav-link:contains("{{") {
    display: flex !important;
    align-items: center !important;
    visibility: visible !important;
}

/* إصلاح زر تبديل الوضع المظلم */
.theme-switch-wrapper {
    display: flex !important;
    align-items: center !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.theme-switch {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* إصلاحات للقوائم الطويلة */
.dropdown-menu {
    max-height: 400px !important;
    overflow-y: auto !important;
}

.dropdown-menu::-webkit-scrollbar {
    width: 6px;
}

.dropdown-menu::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.dropdown-menu::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* إصلاحات للشاشات الصغيرة */
@media (max-width: 991.98px) {
    .navbar-nav .dropdown-menu {
        position: static !important;
        float: none !important;
        width: auto !important;
        margin-top: 0 !important;
        background-color: transparent !important;
        border: 0 !important;
        box-shadow: none !important;
    }

    .navbar-nav .dropdown-item {
        color: rgba(255, 255, 255, 0.9) !important;
        padding: 0.5rem 1rem !important;
    }

    .navbar-nav .dropdown-item:hover {
        color: #ffffff !important;
        background-color: rgba(255, 255, 255, 0.1) !important;
    }
}

/* إصلاحات للوضع المظلم */
[data-theme="dark"] .dropdown-menu {
    background-color: #374151 !important;
    border-color: #4b5563 !important;
}

[data-theme="dark"] .dropdown-item {
    color: #f3f4f6 !important;
}

[data-theme="dark"] .dropdown-item:hover {
    color: #60a5fa !important;
    background-color: rgba(96, 165, 250, 0.1) !important;
}

[data-theme="dark"] .dropdown-divider {
    border-color: #4b5563 !important;
}

[data-theme="dark"] .dropdown-header {
    color: #9ca3af !important;
}

/* تأثيرات الحركة */
.dropdown-menu {
    animation: dropdownFadeIn 0.15s ease-in-out;
}

@keyframes dropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* إصلاحات إضافية للتأكد من الظهور */
.dropdown-menu.show {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    pointer-events: auto !important;
    transform: translateY(0) !important;
}

/* إصلاح مشكلة القطع */
body {
    overflow-x: visible !important;
}

.container,
.container-fluid {
    overflow: visible !important;
}

/* إصلاح خاص للقوائم في الإشعارات */
.notification-dropdown {
    min-width: 300px !important;
    max-width: 400px !important;
}

.notification-item {
    border-bottom: 1px solid #eee;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-text {
    font-size: 0.9rem;
    margin-right: 8px;
    flex: 1;
}
