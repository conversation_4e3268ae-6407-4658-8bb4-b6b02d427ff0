#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from datetime import datetime

def show_all_accounts():
    """عرض جميع الحسابات في النظام"""
    
    # Connect to database
    conn = sqlite3.connect('alemis.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        print("👥 جميع الحسابات في نظام ALEMIS")
        print("=" * 60)
        
        # Get all users
        cursor.execute('''
            SELECT u.*, d.name as department_name 
            FROM users u 
            LEFT JOIN departments d ON u.department_id = d.id 
            ORDER BY u.role, u.first_name
        ''')
        users = cursor.fetchall()
        
        if not users:
            print("❌ لا توجد حسابات في النظام!")
            return
            
        print(f"📊 إجمالي الحسابات: {len(users)}")
        print()
        
        # Group by role
        roles = {}
        for user in users:
            role = user['role']
            if role not in roles:
                roles[role] = []
            roles[role].append(user)
        
        # Role names in Arabic
        role_names = {
            'admin': 'المدير العام',
            'hr': 'الموارد البشرية', 
            'manager': 'مدير المختبر',
            'employee': 'موظف'
        }
        
        # Display accounts by role
        for role, users_in_role in roles.items():
            role_name = role_names.get(role, role)
            print(f"🔹 {role_name} ({len(users_in_role)} حساب):")
            print("-" * 40)
            
            for user in users_in_role:
                dept_name = user['department_name'] if user['department_name'] else 'غير محدد'
                print(f"   👤 {user['first_name']} {user['last_name']}")
                print(f"      📧 اسم المستخدم: {user['username']}")
                print(f"      🏢 القسم: {dept_name}")
                print(f"      📱 الهاتف: {user['phone'] if user['phone'] else 'غير محدد'}")
                print(f"      📧 الإيميل: {user['email'] if user['email'] else 'غير محدد'}")
                print(f"      📅 تاريخ الإنشاء: {user['created_at']}")
                print()
            
        print("🔑 بيانات تسجيل الدخول الافتراضية:")
        print("=" * 40)
        print("👑 المدير العام:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        print()
        print("👔 الموارد البشرية:")
        print("   اسم المستخدم: hr")
        print("   كلمة المرور: hr123")
        print()
        print("🔬 مدير المختبر:")
        print("   اسم المستخدم: manager")
        print("   كلمة المرور: manager123")
        print()
        print("👨‍💼 الموظفون:")
        print("   اسم المستخدم: emp1, emp2, emp3, ...")
        print("   كلمة المرور: emp123")
        print()
        
        # Show departments
        cursor.execute('SELECT * FROM departments ORDER BY name')
        departments = cursor.fetchall()
        
        if departments:
            print("🏢 الأقسام المتاحة:")
            print("-" * 20)
            for dept in departments:
                # Count users in department
                cursor.execute('SELECT COUNT(*) as count FROM users WHERE department_id = ?', (dept['id'],))
                user_count = cursor.fetchone()['count']
                print(f"   🏢 {dept['name']} ({user_count} موظف)")
        
        print()
        print("🌐 رابط الموقع: http://localhost:5000")
        print("📝 ملاحظة: يمكن تغيير كلمات المرور من صفحة الملف الشخصي")
        
    except Exception as e:
        print(f'❌ خطأ: {e}')
    finally:
        conn.close()

if __name__ == '__main__':
    show_all_accounts()
