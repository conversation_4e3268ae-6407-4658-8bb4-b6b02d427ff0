/**
 * إصلاح فوري للقوائم المنسدلة - ALEMEIS System
 * حل مشكلة التأخير وعدم ظهور القوائم في المكان الصحيح
 */

(function() {
    'use strict';

    // تهيئة فورية عند تحميل الصفحة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initInstantDropdowns);
    } else {
        initInstantDropdowns();
    }

    function initInstantDropdowns() {
        console.log('🚀 تهيئة فورية للقوائم المنسدلة...');

        // إزالة جميع مستمعي Bootstrap
        disableBootstrapDropdowns();

        // إعداد القوائم المنسدلة فوراً
        setupInstantDropdowns();

        // إضافة مستمعي الأحداث العامة
        setupGlobalListeners();

        console.log('✅ تم إكمال التهيئة الفورية');
    }

    function disableBootstrapDropdowns() {
        // إزالة جميع خصائص Bootstrap
        const dropdowns = document.querySelectorAll('[data-bs-toggle="dropdown"]');
        dropdowns.forEach(dropdown => {
            dropdown.removeAttribute('data-bs-toggle');
            dropdown.removeAttribute('data-bs-auto-close');
            dropdown.removeAttribute('data-bs-boundary');
        });

        // تعطيل Bootstrap dropdown events
        document.removeEventListener('click', handleBootstrapClick);
    }

    function setupInstantDropdowns() {
        const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
        
        dropdownToggles.forEach((toggle, index) => {
            console.log(`🔧 إعداد القائمة ${index + 1}`);
            
            // إزالة أي مستمعين موجودين
            const newToggle = toggle.cloneNode(true);
            toggle.parentNode.replaceChild(newToggle, toggle);

            // إضافة مستمع النقر الفوري
            newToggle.addEventListener('click', function(e) {
                handleInstantClick(e, this);
            });

            // إعداد القائمة المرتبطة
            const menu = newToggle.nextElementSibling;
            if (menu && menu.classList.contains('dropdown-menu')) {
                setupDropdownMenu(menu);
            }
        });
    }

    function setupDropdownMenu(menu) {
        // إعداد أنماط أساسية للقائمة
        menu.style.position = 'absolute';
        menu.style.zIndex = '1055';
        menu.style.display = 'none';
        menu.style.visibility = 'hidden';
        menu.style.opacity = '0';
        menu.style.pointerEvents = 'none';
        menu.style.transform = 'translateY(-5px) scale(0.98)';
        menu.style.transition = 'all 0.15s ease';
        menu.style.overflow = 'visible';
        menu.style.maxHeight = 'none';
        menu.style.width = 'auto';
        menu.style.minWidth = '220px';
        menu.style.maxWidth = '350px';
    }

    function handleInstantClick(event, toggle) {
        event.preventDefault();
        event.stopPropagation();

        const menu = toggle.nextElementSibling;
        if (!menu || !menu.classList.contains('dropdown-menu')) {
            console.warn('⚠️ لم يتم العثور على قائمة منسدلة');
            return;
        }

        // إغلاق جميع القوائم الأخرى فوراً
        closeAllDropdowns(menu);

        // تبديل حالة القائمة الحالية فوراً
        const isVisible = menu.style.display === 'block';

        if (isVisible) {
            closeDropdownInstant(toggle, menu);
        } else {
            openDropdownInstant(toggle, menu);
        }
    }

    function openDropdownInstant(toggle, menu) {
        console.log('📂 فتح القائمة فوراً');

        // تحديد الموقع أولاً
        positionDropdownInstant(toggle, menu);

        // إظهار القائمة فوراً
        menu.style.display = 'block';
        menu.style.visibility = 'visible';
        menu.style.opacity = '1';
        menu.style.pointerEvents = 'auto';
        menu.style.transform = 'translateY(0) scale(1)';
        menu.classList.add('show');

        // تحديث خصائص الزر
        toggle.setAttribute('aria-expanded', 'true');
        toggle.classList.add('show');
    }

    function closeDropdownInstant(toggle, menu) {
        console.log('📁 إغلاق القائمة فوراً');

        // إخفاء القائمة فوراً
        menu.style.display = 'none';
        menu.style.visibility = 'hidden';
        menu.style.opacity = '0';
        menu.style.pointerEvents = 'none';
        menu.style.transform = 'translateY(-5px) scale(0.98)';
        menu.classList.remove('show');

        // تحديث خصائص الزر
        toggle.setAttribute('aria-expanded', 'false');
        toggle.classList.remove('show');
    }

    function positionDropdownInstant(toggle, menu) {
        try {
            const toggleRect = toggle.getBoundingClientRect();
            const parentDropdown = toggle.closest('.dropdown');

            if (!parentDropdown) return;

            // تحديد الموقع بناءً على موقع الزر
            const isRightSide = toggleRect.left > window.innerWidth / 2;
            const isInNavbar = toggle.closest('.navbar');

            // إعداد الموقع الأساسي
            menu.style.position = 'absolute';
            menu.style.zIndex = '1055';
            menu.style.overflow = 'visible';
            menu.style.maxHeight = 'none';
            menu.style.width = 'auto';

            if (isInNavbar) {
                // للقوائم في الشريط العلوي
                menu.style.top = 'calc(100% + 2px)';
                menu.style.bottom = 'auto';

                if (isRightSide || menu.classList.contains('dropdown-menu-end')) {
                    menu.style.right = '0';
                    menu.style.left = 'auto';
                } else {
                    menu.style.left = '0';
                    menu.style.right = 'auto';
                }
            } else {
                // للقوائم العادية
                menu.style.top = 'calc(100% + 2px)';
                menu.style.bottom = 'auto';
                menu.style.left = '0';
                menu.style.right = 'auto';
            }

            // إزالة أي قيود على الحجم
            menu.style.minWidth = '220px';
            menu.style.maxWidth = '350px';

            // تصحيح فوري للموقع
            requestAnimationFrame(() => {
                adjustDropdownPosition(menu);
            });

        } catch (error) {
            console.error('❌ خطأ في تحديد الموقع:', error);
        }
    }

    function adjustDropdownPosition(menu) {
        const menuRect = menu.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // تصحيح الموقع الأفقي
        if (menuRect.right > viewportWidth - 10) {
            menu.style.left = 'auto';
            menu.style.right = '0';
        }

        if (menuRect.left < 10) {
            menu.style.left = '0';
            menu.style.right = 'auto';
        }

        // تصحيح الموقع العمودي
        if (menuRect.bottom > viewportHeight - 10) {
            menu.style.top = 'auto';
            menu.style.bottom = '100%';
        }

        if (menuRect.top < 10) {
            menu.style.top = '100%';
            menu.style.bottom = 'auto';
        }
    }

    function closeAllDropdowns(exceptMenu = null) {
        const allMenus = document.querySelectorAll('.dropdown-menu');
        allMenus.forEach(menu => {
            if (menu !== exceptMenu) {
                const toggle = menu.previousElementSibling;
                if (toggle && toggle.classList.contains('dropdown-toggle')) {
                    closeDropdownInstant(toggle, menu);
                }
            }
        });
    }

    function setupGlobalListeners() {
        // إغلاق عند النقر خارج القوائم
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.dropdown')) {
                closeAllDropdowns();
            }
        });

        // إغلاق بمفتاح Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeAllDropdowns();
            }
        });

        // إعادة تحديد الموقع عند تغيير حجم النافذة
        window.addEventListener('resize', function() {
            const openMenus = document.querySelectorAll('.dropdown-menu.show');
            openMenus.forEach(menu => {
                const toggle = menu.previousElementSibling;
                if (toggle) {
                    positionDropdownInstant(toggle, menu);
                }
            });
        });
    }

    function handleBootstrapClick(e) {
        // منع Bootstrap من التدخل
        e.stopImmediatePropagation();
    }

    // تصدير الدوال للاستخدام الخارجي
    window.InstantDropdown = {
        init: initInstantDropdowns,
        closeAll: closeAllDropdowns,
        open: function(toggleSelector) {
            const toggle = document.querySelector(toggleSelector);
            if (toggle) {
                const menu = toggle.nextElementSibling;
                if (menu) {
                    openDropdownInstant(toggle, menu);
                }
            }
        },
        close: function(toggleSelector) {
            const toggle = document.querySelector(toggleSelector);
            if (toggle) {
                const menu = toggle.nextElementSibling;
                if (menu) {
                    closeDropdownInstant(toggle, menu);
                }
            }
        }
    };

    console.log('📦 تم تحميل وحدة القوائم المنسدلة الفورية');
})();
