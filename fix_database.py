#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from datetime import datetime

def fix_database():
    """إصلاح قاعدة البيانات وإضافة الأعمدة المفقودة"""
    
    # Connect to database
    conn = sqlite3.connect('alemis.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        print("🔧 بدء إصلاح قاعدة البيانات...")
        
        # Check if columns exist in leave_types table
        cursor.execute('PRAGMA table_info(leave_types)')
        columns = [col[1] for col in cursor.fetchall()]
        print(f"📋 الأعمدة الحالية في جدول leave_types: {columns}")
        
        # Add missing columns if needed
        if 'requires_approval' not in columns:
            cursor.execute('ALTER TABLE leave_types ADD COLUMN requires_approval INTEGER DEFAULT 1')
            print('✅ تم إضافة عمود requires_approval')
        
        if 'max_consecutive_days' not in columns:
            cursor.execute('ALTER TABLE leave_types ADD COLUMN max_consecutive_days INTEGER DEFAULT 30')
            print('✅ تم إضافة عمود max_consecutive_days')
        
        # Check if 'بدل يوم تغطية' leave type exists
        cursor.execute('SELECT * FROM leave_types WHERE name = ?', ('بدل يوم تغطية',))
        existing = cursor.fetchone()
        
        if not existing:
            # Add new leave type
            cursor.execute('''
                INSERT INTO leave_types (name, description, default_days, requires_approval, max_consecutive_days)
                VALUES (?, ?, ?, ?, ?)
            ''', ('بدل يوم تغطية', 'إجازة بدل أيام التغطية والعمل في الإجازات الرسمية', 0, 1, 30))
            
            print('✅ تم إضافة نوع إجازة بدل يوم تغطية')
            
            # Get the new leave type ID
            leave_type_id = cursor.lastrowid
            
            # Create balances for all existing users
            cursor.execute('SELECT id FROM users')
            users = cursor.fetchall()
            
            current_year = datetime.now().year
            
            for user in users:
                user_id = user['id']
                # Check if balance already exists
                cursor.execute('''
                    SELECT * FROM leave_balances 
                    WHERE user_id = ? AND leave_type_id = ? AND year = ?
                ''', (user_id, leave_type_id, current_year))
                
                if not cursor.fetchone():
                    cursor.execute('''
                        INSERT INTO leave_balances (user_id, leave_type_id, year, total_days, used_days, remaining_days)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (user_id, leave_type_id, current_year, 0, 0, 0))
            
            print(f'✅ تم إنشاء أرصدة بدل يوم تغطية لـ {len(users)} موظف')
        else:
            print('⚠️ نوع إجازة بدل يوم تغطية موجود بالفعل')
        
        # Check if document column exists in leave_requests table
        cursor.execute('PRAGMA table_info(leave_requests)')
        leave_columns = [col[1] for col in cursor.fetchall()]
        
        if 'document' not in leave_columns:
            cursor.execute('ALTER TABLE leave_requests ADD COLUMN document TEXT')
            print('✅ تم إضافة عمود document إلى جدول leave_requests')
        
        # Commit changes
        conn.commit()
        print('✅ تم حفظ جميع التغييرات')
        
        # Show current leave types
        cursor.execute('SELECT * FROM leave_types ORDER BY id')
        leave_types = cursor.fetchall()
        
        print('\n📋 أنواع الإجازات الحالية:')
        for lt in leave_types:
            print(f"  - {lt['name']} (الأيام الافتراضية: {lt['default_days']})")
        
        print('\n✅ تم إصلاح قاعدة البيانات بنجاح!')
        
    except Exception as e:
        print(f'❌ خطأ: {e}')
        conn.rollback()
    finally:
        conn.close()

if __name__ == '__main__':
    fix_database()
