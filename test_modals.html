<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النوافذ المنبثقة - ALEMEIS</title>
    
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    
    <!-- Modal Fix CSS -->
    <link rel="stylesheet" href="static/css/modal-fix.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 2rem;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #0ea5e9;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: #f8fafc;
            border-radius: 8px;
            border-right: 4px solid #0ea5e9;
        }
        
        .btn-test {
            margin: 0.5rem;
            min-width: 150px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="text-primary">
                <i class="fas fa-window-restore me-2"></i>
                اختبار النوافذ المنبثقة
            </h1>
            <p class="text-muted">نظام ALEMEIS - شركة العميس الطبية</p>
        </div>

        <!-- اختبار النوافذ الأساسية -->
        <div class="test-section">
            <h3><i class="fas fa-play-circle text-success me-2"></i>النوافذ الأساسية</h3>
            <p>اختبار النوافذ المنبثقة الأساسية مع أحجام مختلفة</p>
            
            <button type="button" class="btn btn-primary btn-test" data-bs-toggle="modal" data-bs-target="#basicModal">
                <i class="fas fa-window-maximize me-1"></i>نافذة أساسية
            </button>
            
            <button type="button" class="btn btn-success btn-test" data-bs-toggle="modal" data-bs-target="#largeModal">
                <i class="fas fa-expand me-1"></i>نافذة كبيرة
            </button>
            
            <button type="button" class="btn btn-warning btn-test" data-bs-toggle="modal" data-bs-target="#smallModal">
                <i class="fas fa-compress me-1"></i>نافذة صغيرة
            </button>
        </div>

        <!-- اختبار نوافذ التأكيد -->
        <div class="test-section">
            <h3><i class="fas fa-exclamation-triangle text-warning me-2"></i>نوافذ التأكيد</h3>
            <p>اختبار نوافذ التأكيد والحذف</p>
            
            <button type="button" class="btn btn-danger btn-test" data-bs-toggle="modal" data-bs-target="#confirmModal">
                <i class="fas fa-trash me-1"></i>تأكيد الحذف
            </button>
            
            <button type="button" class="btn btn-info btn-test" data-bs-toggle="modal" data-bs-target="#infoModal">
                <i class="fas fa-info-circle me-1"></i>معلومات
            </button>
        </div>

        <!-- اختبار النماذج -->
        <div class="test-section">
            <h3><i class="fas fa-edit text-primary me-2"></i>نوافذ النماذج</h3>
            <p>اختبار النوافذ التي تحتوي على نماذج</p>
            
            <button type="button" class="btn btn-secondary btn-test" data-bs-toggle="modal" data-bs-target="#formModal">
                <i class="fas fa-plus me-1"></i>إضافة جديد
            </button>
            
            <button type="button" class="btn btn-dark btn-test" data-bs-toggle="modal" data-bs-target="#editModal">
                <i class="fas fa-edit me-1"></i>تعديل
            </button>
        </div>

        <!-- حالة الاختبار -->
        <div class="test-section">
            <h3><i class="fas fa-check-circle text-success me-2"></i>حالة الاختبار</h3>
            <div id="testStatus" class="alert alert-info">
                <i class="fas fa-info-circle me-1"></i>
                انقر على الأزرار أعلاه لاختبار النوافذ المنبثقة
            </div>
        </div>
    </div>

    <!-- النوافذ المنبثقة -->
    
    <!-- نافذة أساسية -->
    <div class="modal fade" id="basicModal" tabindex="-1" aria-labelledby="basicModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-primary">
                    <h5 class="modal-title" id="basicModalLabel">
                        <i class="fas fa-window-maximize me-2"></i>نافذة أساسية
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هذه نافذة منبثقة أساسية للاختبار.</p>
                    <p>يجب أن تظهر وتختفي بسلاسة مع تأثيرات حركية جميلة.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>إغلاق
                    </button>
                    <button type="button" class="btn btn-primary">
                        <i class="fas fa-check me-1"></i>موافق
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة كبيرة -->
    <div class="modal fade" id="largeModal" tabindex="-1" aria-labelledby="largeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-success">
                    <h5 class="modal-title" id="largeModalLabel">
                        <i class="fas fa-expand me-2"></i>نافذة كبيرة
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>العمود الأول</h6>
                            <p>محتوى العمود الأول في النافذة الكبيرة.</p>
                        </div>
                        <div class="col-md-6">
                            <h6>العمود الثاني</h6>
                            <p>محتوى العمود الثاني في النافذة الكبيرة.</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة صغيرة -->
    <div class="modal fade" id="smallModal" tabindex="-1" aria-labelledby="smallModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-warning">
                    <h5 class="modal-title" id="smallModalLabel">
                        <i class="fas fa-compress me-2"></i>نافذة صغيرة
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <p>نافذة صغيرة للاختبار</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-warning" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تأكيد -->
    <div class="modal fade" id="confirmModal" tabindex="-1" aria-labelledby="confirmModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered confirm-modal">
            <div class="modal-content">
                <div class="modal-header bg-danger">
                    <h5 class="modal-title" id="confirmModalLabel">
                        <i class="fas fa-exclamation-triangle me-2"></i>تأكيد الحذف
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <i class="fas fa-trash-alt text-danger" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                    <h6>هل أنت متأكد من الحذف؟</h6>
                    <p class="text-muted">لا يمكن التراجع عن هذا الإجراء</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>إلغاء
                    </button>
                    <button type="button" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>حذف
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة معلومات -->
    <div class="modal fade" id="infoModal" tabindex="-1" aria-labelledby="infoModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-info">
                    <h5 class="modal-title" id="infoModalLabel">
                        <i class="fas fa-info-circle me-2"></i>معلومات النظام
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-hospital me-2"></i>
                        <strong>نظام ALEMEIS</strong> - نظام إدارة موظفين المختبر
                    </div>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>النوافذ المنبثقة تعمل بشكل صحيح</li>
                        <li><i class="fas fa-check text-success me-2"></i>التأثيرات الحركية مفعلة</li>
                        <li><i class="fas fa-check text-success me-2"></i>الاستجابة للأجهزة المختلفة</li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-info" data-bs-dismiss="modal">
                        <i class="fas fa-check me-1"></i>فهمت
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة نموذج -->
    <div class="modal fade" id="formModal" tabindex="-1" aria-labelledby="formModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered form-modal">
            <div class="modal-content">
                <div class="modal-header bg-primary">
                    <h5 class="modal-title" id="formModalLabel">
                        <i class="fas fa-plus me-2"></i>إضافة جديد
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="mb-3">
                            <label for="name" class="form-label">الاسم</label>
                            <input type="text" class="form-control" id="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" required>
                        </div>
                        <div class="mb-3">
                            <label for="department" class="form-label">القسم</label>
                            <select class="form-select" id="department" required>
                                <option value="">اختر القسم</option>
                                <option value="lab">المختبر</option>
                                <option value="admin">الإدارة</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>إلغاء
                    </button>
                    <button type="button" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>حفظ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تعديل -->
    <div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, #6b7280, #4b5563); color: white;">
                    <h5 class="modal-title" id="editModalLabel">
                        <i class="fas fa-edit me-2"></i>تعديل البيانات
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكد من صحة البيانات قبل الحفظ
                    </div>
                    <form>
                        <div class="mb-3">
                            <label for="editName" class="form-label">الاسم</label>
                            <input type="text" class="form-control" id="editName" value="أحمد محمد">
                        </div>
                        <div class="mb-3">
                            <label for="editPosition" class="form-label">المنصب</label>
                            <input type="text" class="form-control" id="editPosition" value="فني مختبر">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>إلغاء
                    </button>
                    <button type="button" class="btn btn-success">
                        <i class="fas fa-check me-1"></i>تحديث
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Modal Fix JS -->
    <script src="static/js/modal-fix.js"></script>
    
    <script>
        // تحديث حالة الاختبار
        document.addEventListener('DOMContentLoaded', function() {
            const testStatus = document.getElementById('testStatus');
            
            // مراقبة فتح النوافذ
            document.querySelectorAll('.modal').forEach(modal => {
                modal.addEventListener('shown.bs.modal', function() {
                    testStatus.innerHTML = `
                        <i class="fas fa-check-circle text-success me-1"></i>
                        تم فتح النافذة: <strong>${this.id}</strong> بنجاح!
                    `;
                    testStatus.className = 'alert alert-success';
                });
                
                modal.addEventListener('hidden.bs.modal', function() {
                    testStatus.innerHTML = `
                        <i class="fas fa-times-circle text-warning me-1"></i>
                        تم إغلاق النافذة: <strong>${this.id}</strong>
                    `;
                    testStatus.className = 'alert alert-warning';
                });
            });
        });
    </script>
</body>
</html>
