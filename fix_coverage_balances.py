#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from datetime import datetime

def fix_coverage_leave_balances():
    """إصلاح أرصدة بدل يوم تغطية لجميع المستخدمين"""
    
    # Connect to database
    conn = sqlite3.connect('alemis.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        print("🔧 إصلاح أرصدة بدل يوم تغطية...")
        print("=" * 50)
        
        # Get coverage leave type
        cursor.execute('SELECT * FROM leave_types WHERE name = ?', ('بدل يوم تغطية',))
        coverage_leave_type = cursor.fetchone()
        
        if not coverage_leave_type:
            print("❌ نوع إجازة 'بدل يوم تغطية' غير موجود!")
            return
            
        leave_type_id = coverage_leave_type['id']
        current_year = datetime.now().year
        
        print(f"✅ نوع إجازة 'بدل يوم تغطية' موجود (ID: {leave_type_id})")
        
        # Get all users
        cursor.execute('SELECT id, first_name, last_name, role FROM users ORDER BY role, first_name')
        all_users = cursor.fetchall()
        
        print(f"👥 إجمالي المستخدمين: {len(all_users)}")
        
        # Check existing balances
        cursor.execute('''
            SELECT user_id FROM leave_balances 
            WHERE leave_type_id = ? AND year = ?
        ''', (leave_type_id, current_year))
        existing_user_ids = set(row['user_id'] for row in cursor.fetchall())
        
        print(f"📊 المستخدمون الذين لديهم أرصدة بالفعل: {len(existing_user_ids)}")
        
        # Find users without balances
        users_without_balances = []
        for user in all_users:
            if user['id'] not in existing_user_ids:
                users_without_balances.append(user)
                
        print(f"⚠️ المستخدمون بدون أرصدة: {len(users_without_balances)}")
        
        if users_without_balances:
            print("\nإضافة أرصدة للمستخدمين التاليين:")
            for user in users_without_balances:
                print(f"   - {user['first_name']} {user['last_name']} ({user['role']})")
                
                # Add balance for this user
                cursor.execute('''
                    INSERT INTO leave_balances (user_id, leave_type_id, year, total_days, used_days, remaining_days)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (user['id'], leave_type_id, current_year, 0, 0, 0))
                
            conn.commit()
            print(f"\n✅ تم إضافة أرصدة بدل يوم تغطية لـ {len(users_without_balances)} مستخدم")
        else:
            print("\n✅ جميع المستخدمين لديهم أرصدة بدل يوم تغطية")
            
        # Final verification
        cursor.execute('''
            SELECT COUNT(*) as count FROM leave_balances 
            WHERE leave_type_id = ? AND year = ?
        ''', (leave_type_id, current_year))
        final_count = cursor.fetchone()['count']
        
        print(f"\n📈 إجمالي أرصدة بدل يوم تغطية بعد الإصلاح: {final_count}")
        
        # Show sample of all balances
        cursor.execute('''
            SELECT u.first_name, u.last_name, u.role, lb.total_days, lb.used_days, lb.remaining_days
            FROM leave_balances lb
            JOIN users u ON lb.user_id = u.id
            WHERE lb.leave_type_id = ? AND lb.year = ?
            ORDER BY u.role, u.first_name
        ''', (leave_type_id, current_year))
        all_balances = cursor.fetchall()
        
        print(f"\n📋 جميع أرصدة بدل يوم تغطية ({len(all_balances)}):")
        for balance in all_balances:
            print(f"   - {balance['first_name']} {balance['last_name']} ({balance['role']}): "
                  f"إجمالي={balance['total_days']}, مستخدم={balance['used_days']}, متبقي={balance['remaining_days']}")
                  
    except Exception as e:
        print(f'❌ خطأ: {e}')
        conn.rollback()
    finally:
        conn.close()

if __name__ == '__main__':
    fix_coverage_leave_balances()
